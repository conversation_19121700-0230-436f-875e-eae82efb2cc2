#if !FUSION_DEV

#region Assets/Photon/Fusion/Editor/AssemblyAttributes/FusionEditorAssemblyAttributes.Common.cs

// merged EditorAssemblyAttributes

#region RegisterEditorLoader.cs

// the default edit-mode loader
[assembly: Fusion.Editor.FusionGlobalScriptableObjectEditorAttribute(typeof(Fusion.FusionGlobalScriptableObject), AllowEditMode = true, Order = int.MaxValue)]

#endregion



#endregion

#endif
