fileFormatVersion: 2
guid: b2a5c33c14bcf6d47b07599e5050d51b
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 1
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 2
    wrapV: 2
    wrapW: 2
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 100
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 4
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 3
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 4
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: GrassSpriteTest_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fee36cc9f37cea24e80a488db57124fb
      internalID: -1790886751
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_1
      rect:
        serializedVersion: 2
        x: 484
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e6e856b547fbe3b4a82ff71f9baae9c2
      internalID: -1945898615
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_2
      rect:
        serializedVersion: 2
        x: 968
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1580ed81028bee2409eb66ea11e55073
      internalID: -1759614364
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_3
      rect:
        serializedVersion: 2
        x: 1452
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2e31177293b5140489d383deefc8d372
      internalID: 1982556173
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_4
      rect:
        serializedVersion: 2
        x: 1936
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2fe5c037151fe0c4eb2d1c3b152f4854
      internalID: -1484033220
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_5
      rect:
        serializedVersion: 2
        x: 2420
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c611c892feea94544b6da87d398534d0
      internalID: -1151973772
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_6
      rect:
        serializedVersion: 2
        x: 2904
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e88c497602b601840bafa5a7e16f88e5
      internalID: -1010911544
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: GrassSpriteTest_7
      rect:
        serializedVersion: 2
        x: 3388
        y: 0
        width: 484
        height: 736
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      customData: 
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 4d671475be5faff4a9dcdf9a96c7f197
      internalID: 1427298764
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    customData: 
    physicsShape: []
    bones: []
    spriteID: d0112a66443cca04f9b4a54269ee85cd
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    spriteCustomMetadata:
      entries: []
    nameFileIdTable:
      GrassSpriteTest_0: -1790886751
      GrassSpriteTest_1: -1945898615
      GrassSpriteTest_2: -1759614364
      GrassSpriteTest_3: 1982556173
      GrassSpriteTest_4: -1484033220
      GrassSpriteTest_5: -1151973772
      GrassSpriteTest_6: -1010911544
      GrassSpriteTest_7: 1427298764
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
