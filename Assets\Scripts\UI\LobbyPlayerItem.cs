using UnityEngine;
using TMPro;
using Fusion;

public class LobbyPlayerItem : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI _playerNameText;
    [SerializeField] private GameObject _hostIndicator;

    public void Initialize(PlayerRef player, bool isHost)
    {
        if (_playerNameText != null)
        {
            _playerNameText.text = isHost ? $"Player {player} (Host)" : $"Player {player}";
        }

        if (_hostIndicator != null)
        {
            _hostIndicator.SetActive(isHost);
        }
    }
}
