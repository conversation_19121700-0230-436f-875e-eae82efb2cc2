using System.Collections.Generic;
using UnityEngine;
using Fusion;
using Projectiles;

/// <summary>
/// Team-based game mode where players are divided into teams.
/// Teams can compete against each other while fighting waves of enemies.
/// </summary>
public class TeamBasedGameMode : GameModeBase
{
    [Header("Team Settings")]
    [SerializeField] private int numberOfTeams = 2;
    [SerializeField] private Color[] teamColors = new Color[]
    {
        new(0.8f, 0.2f, 0.2f), // Red
        new(0.2f, 0.2f, 0.8f), // Blue
        new(0.2f, 0.8f, 0.2f), // Green
        new(0.8f, 0.8f, 0.2f)  // Yellow
    };

    [Header("Wave Settings")]
    [SerializeField] private float initialCooldown = 30f;
    [SerializeField] private float cooldownBetweenWaves = 20f;
    [SerializeField] private int baseEnemiesPerWave = 10;
    [SerializeField] private float enemyIncreasePerWave = 5f;
    [SerializeField] private int maxActiveEnemies = 10;

    [Header("Enemy Spawning")]
    [SerializeField] private NetworkPrefabRef[] enemyPrefabs;
    [SerializeField] private float timeBetweenSpawns = 1f;

    [Header("Wave Configuration")]
    [SerializeField] private WaveConfig waveConfig;

    // Team tracking
    [Networked, Capacity(8)]
    public NetworkDictionary<PlayerRef, int> PlayerTeams { get; }

    [Networked, Capacity(4)]
    public NetworkArray<int> TeamScores { get; }

    // Runtime variables
    private readonly List<NetworkObject> activeEnemies = new();
    private EnemySpawnPoint[] spawnPoints;
    private EnemySpawnPoint lastUsedSpawnPoint;

    [Networked] private TickTimer WaveCooldownTimer { get; set; }
    [Networked] private TickTimer SpawnTimer { get; set; }
    [Networked] private int EnemiesRemainingToSpawn { get; set; }
    [Networked] private int TotalEnemiesInWave { get; set; }
    [Networked] private int EnemiesKilledThisWave { get; set; }

    public override void Spawned()
    {
        base.Spawned();

        if (Runner.IsServer)
        {
            // Find spawn points
            spawnPoints = Runner.SimulationUnityScene.FindObjectsOfTypeInOrder<EnemySpawnPoint>(false);

            // Clean up any existing enemies
            CleanupEnemies();

            // Initialize team scores
            for (int i = 0; i < TeamScores.Length; i++)
            {
                if (i < numberOfTeams)
                {
                    TeamScores.Set(i, 0);
                }
            }
        }
    }

    public override void StartGame()
    {
        if (!Runner.IsServer) return;

        IsGameActive = true;
        CurrentWave = 0;
        IsGameOver = false;

        // Assign players to teams
        AssignPlayersToTeams();

        // Start initial cooldown
        WaveCooldownTimer = TickTimer.CreateFromSeconds(Runner, initialCooldown);

        // Notify listeners (this will also apply weapons to existing players)
        NotifyGameStarted();

        Debug.Log("Team-Based Mode game started!");
    }

    private void AssignPlayersToTeams()
    {
        if (!Runner.IsServer) return;

        // Get all players from the Gameplay component
        var gameplay = Context.Gameplay;
        if (gameplay == null) return;

        List<PlayerRef> playerRefs = new();
        foreach (var kvp in gameplay.Players)
        {
            playerRefs.Add(kvp.Key);
        }

        // Assign players to teams (round-robin style)
        for (int i = 0; i < playerRefs.Count; i++)
        {
            int teamIndex = i % numberOfTeams;
            PlayerTeams.Add(playerRefs[i], teamIndex);

            // Apply team color to player if needed
            if (teamIndex < teamColors.Length)
            {
                // Get the player and apply team color
                Player player = gameplay.Players[playerRefs[i]];
                ApplyTeamColor(player, teamColors[teamIndex]);
            }

            Debug.Log($"Assigned player {playerRefs[i]} to team {teamIndex}");
        }
    }

    private void ApplyTeamColor(Player player, Color teamColor)
    {
        // Get the player agent and apply team color
        var agent = player.ActiveAgent;
        if (agent != null)
        {
            // Apply team color to the player agent
            // This would depend on your specific implementation
            // For example, you might have a TeamColorApplier component on the agent
            if (agent.TryGetComponent(out TeamColorApplier colorApplier))
            {
                colorApplier.ApplyColor(teamColor);
            }
            else
            {
                Debug.LogWarning($"Player agent does not have a TeamColorApplier component");
            }
        }
    }

    public override void EndGame()
    {
        if (!Runner.IsServer) return;

        IsGameActive = false;
        IsGameOver = true;

        // Determine winning team
        int winningTeam = DetermineWinningTeam();

        // Clean up any remaining enemies
        CleanupEnemies();

        // Notify listeners
        NotifyGameOver();

        Debug.Log($"Team-Based Mode game ended! Winning team: {winningTeam}");
    }

    private int DetermineWinningTeam()
    {
        int highestScore = -1;
        int winningTeam = -1;

        for (int i = 0; i < numberOfTeams; i++)
        {
            int score = TeamScores[i];
            if (score > highestScore)
            {
                highestScore = score;
                winningTeam = i;
            }
        }

        return winningTeam;
    }

    public override void StartNextWave()
    {
        if (!Runner.IsServer) return;

        CurrentWave++;

        // Calculate enemies for this wave
        int enemiesThisWave;

        if (waveConfig != null)
        {
            // Use wave config if available
            enemiesThisWave = waveConfig.CalculateEnemyCount(CurrentWave - 1, difficultyMultiplier);
        }
        else
        {
            // Fallback to simple calculation
            enemiesThisWave = baseEnemiesPerWave + Mathf.FloorToInt(enemyIncreasePerWave * (CurrentWave - 1) * difficultyMultiplier);
        }

        TotalEnemiesInWave = enemiesThisWave;
        EnemiesRemainingToSpawn = enemiesThisWave;
        EnemiesKilledThisWave = 0;

        // Initialize spawn timer
        SpawnTimer = TickTimer.CreateFromSeconds(Runner, timeBetweenSpawns);

        // Notify listeners
        NotifyWaveStarted(CurrentWave);

        Debug.Log($"Wave {CurrentWave} started with {TotalEnemiesInWave} enemies!");
    }

    public override void HandlePlayerJoined(Player player)
    {
        if (!Runner.IsServer) return;

        // Assign the new player to the team with fewer players
        int[] teamCounts = new int[numberOfTeams];
        foreach (var kvp in PlayerTeams)
        {
            teamCounts[kvp.Value]++;
        }

        // Find the team with the fewest players
        int targetTeam = 0;
        for (int i = 1; i < teamCounts.Length; i++)
        {
            if (teamCounts[i] < teamCounts[targetTeam])
                targetTeam = i;
        }

        // Assign player to team
        PlayerTeams.Add(player.Object.InputAuthority, targetTeam);

        // Apply team color
        if (targetTeam < teamColors.Length)
        {
            ApplyTeamColor(player, teamColors[targetTeam]);
        }

        Debug.Log($"Player joined Team-Based Mode: {player.Object.InputAuthority}, assigned to team {targetTeam}");

        // Apply starting weapons to the player
        ApplyStartingWeaponsToPlayer(player);
    }

    public override void HandlePlayerLeft(Player player)
    {
        if (!Runner.IsServer) return;

        // Remove player from team tracking
        PlayerTeams.Remove(player.Object.InputAuthority);

        Debug.Log($"Player left Team-Based Mode: {player.Object.InputAuthority}");

        // Check if all players left and handle accordingly
        if (Context.Gameplay != null && Context.Gameplay.PlayerCount == 0)
        {
            Debug.Log("All players left, ending game");
            EndGame();
        }
    }

    public override void FixedUpdateNetwork()
    {
        if (!Runner.IsServer) return;

        if (IsGameActive && !IsGameOver)
        {
            // Check if we're in cooldown between waves
            if (WaveCooldownTimer.IsRunning && WaveCooldownTimer.Expired(Runner))
            {
                StartNextWave();
                WaveCooldownTimer = default; // Clear the timer
            }

            // If we're in an active wave, spawn enemies
            if (EnemiesRemainingToSpawn > 0 && activeEnemies.Count < maxActiveEnemies)
            {
                if (SpawnTimer.Expired(Runner))
                {
                    if (spawnPoints != null && spawnPoints.Length > 0)
                    {
                        SpawnEnemy();
                    }

                    // Reset spawn timer
                    SpawnTimer = TickTimer.CreateFromSeconds(Runner, timeBetweenSpawns);
                }
            }

            // Check if wave is complete
            if (EnemiesRemainingToSpawn <= 0 && activeEnemies.Count == 0 && EnemiesKilledThisWave >= TotalEnemiesInWave)
            {
                // Wave completed
                NotifyWaveCompleted(CurrentWave);

                Debug.Log($"Wave {CurrentWave} completed!");

                // Check if game is over
                if (!isEndless && CurrentWave >= maxWaves)
                {
                    Debug.Log("Max waves reached, ending game");
                    EndGame();
                }
                else
                {
                    // Start cooldown for next wave
                    float nextWaveCooldown = cooldownBetweenWaves;

                    // Use wave config if available
                    if (waveConfig != null)
                    {
                        var waveInfo = waveConfig.GetWaveInfo(CurrentWave - 1);
                        if (waveInfo != null)
                        {
                            nextWaveCooldown = waveInfo.cooldownAfterWave;
                        }
                    }

                    WaveCooldownTimer = TickTimer.CreateFromSeconds(Runner, nextWaveCooldown);

                    Debug.Log($"Next wave in {nextWaveCooldown} seconds");
                }
            }
        }
    }

    private void SpawnEnemy()
    {
        // Similar to HordeGameMode
        if (spawnPoints.Length == 0 || EnemiesRemainingToSpawn <= 0) return;

        // Select a random spawn point (avoiding the last used one if possible)
        EnemySpawnPoint spawnPoint;
        do
        {
            spawnPoint = spawnPoints[Random.Range(0, spawnPoints.Length)];
        }
        while (spawnPoint == lastUsedSpawnPoint && spawnPoints.Length > 1);

        lastUsedSpawnPoint = spawnPoint;

        // Select an enemy prefab
        NetworkPrefabRef enemyPrefab;

        if (waveConfig != null)
        {
            // Use wave config to select enemy
            enemyPrefab = waveConfig.SelectEnemyPrefab(CurrentWave - 1, difficultyMultiplier);
        }
        else
        {
            // Fallback to random selection from array
            enemyPrefab = enemyPrefabs[Random.Range(0, enemyPrefabs.Length)];
        }

        // Ensure we have a valid prefab
        if (enemyPrefab == default)
        {
            Debug.LogError("No valid enemy prefab selected!");
            return;
        }

        // Spawn the enemy
        NetworkObject enemyObj = Runner.Spawn(enemyPrefab, spawnPoint.transform.position, Quaternion.identity);

        // Set up the enemy
        if (enemyObj.TryGetComponent(out EnemyAIBase enemy))
        {
            enemy.OnDeath += HandleEnemyDeath;
            activeEnemies.Add(enemyObj);
            EnemiesRemainingToSpawn--;

            Debug.Log($"Spawned enemy at {spawnPoint.name}, {EnemiesRemainingToSpawn} remaining to spawn");
        }
        else
        {
            Debug.LogError("Spawned object does not have EnemyAIBase component!");
            Runner.Despawn(enemyObj);
        }
    }

    private void HandleEnemyDeath(NetworkObject enemyObj)
    {
        activeEnemies.Remove(enemyObj);
        EnemiesKilledThisWave++;

        // Determine which player/team gets credit for the kill
        // Get the Health component to find the last player who hit this enemy
        if (enemyObj.TryGetComponent(out Health enemyHealth))
        {
            // Subscribe to the FatalHitTaken event to get the killer information
            // This is a one-time subscription that will be automatically cleaned up
            // when the enemy is despawned
            enemyHealth.FatalHitTaken += (hitData) => {
                // Check if the InstigatorRef is valid (not default)
                if (hitData.InstigatorRef != default)
                {
                    // Get the team of the player who killed the enemy
                    if (PlayerTeams.TryGet(hitData.InstigatorRef, out int teamIndex))
                    {
                        // Award points to the team
                        if (teamIndex >= 0 && teamIndex < TeamScores.Length)
                        {
                            TeamScores.Set(teamIndex, TeamScores[teamIndex] + 1);
                            Debug.Log($"Team {teamIndex} scored a point! Total: {TeamScores[teamIndex]}");
                        }
                    }
                }
            };
        }

        Debug.Log($"Enemy killed, {activeEnemies.Count} active enemies remaining, {EnemiesKilledThisWave}/{TotalEnemiesInWave} killed this wave");
    }

    private void CleanupEnemies()
    {
        // Clean up any existing enemies
        foreach (var enemy in activeEnemies)
        {
            if (enemy && enemy.IsValid)
            {
                Runner.Despawn(enemy);
            }
        }
        activeEnemies.Clear();
    }

    // Get a player's team
    public int GetPlayerTeam(PlayerRef playerRef)
    {
        if (PlayerTeams.TryGet(playerRef, out int team))
        {
            return team;
        }
        return -1; // No team assigned
    }

    // Get team score
    public int GetTeamScore(int teamIndex)
    {
        if (teamIndex >= 0 && teamIndex < TeamScores.Length)
        {
            return TeamScores[teamIndex];
        }
        return 0;
    }

    // Apply configuration from a WaveConfig
    public void ApplyWaveConfig(WaveConfig config)
    {
        if (!HasStateAuthority || config == null) return;

        waveConfig = config;
        isEndless = config.isEndless;
        difficultyMultiplier = config.globalDifficultyMultiplier;

        Debug.Log($"Applied wave config: Endless={isEndless}, Difficulty={difficultyMultiplier}");
    }

    // Set number of teams
    public void SetNumberOfTeams(int teams)
    {
        if (!HasStateAuthority) return;

        numberOfTeams = Mathf.Clamp(teams, 2, 4);
        Debug.Log($"Set number of teams to {numberOfTeams}");
    }
}
