{"buildFiles": ["C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.40f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\4v6w3k1o\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.40f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\4v6w3k1o\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "game", "output": "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\4v6w3k1o\\obj\\arm64-v8a\\libgame.so", "runtimeFiles": []}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.40f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.40f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\NDK\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}