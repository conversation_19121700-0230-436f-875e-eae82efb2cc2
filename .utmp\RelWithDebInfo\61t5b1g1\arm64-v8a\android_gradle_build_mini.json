{"buildFiles": ["C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\src\\main\\cpp\\GameActivity\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.37f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Program Files\\Unity\\Hub\\Editor\\6000.0.37f1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\SDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"game::@d02bb112ea9f9c2ed29f": {"artifactName": "game", "abi": "arm64-v8a", "output": "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\Library\\Bee\\Android\\Prj\\IL2CPP\\Gradle\\unityLibrary\\build\\intermediates\\cxx\\RelWithDebInfo\\61t5b1g1\\obj\\arm64-v8a\\libgame.so", "runtimeFiles": []}}}