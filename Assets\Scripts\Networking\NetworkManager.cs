using System;
using System.Collections.Generic;
using UnityEngine;
using Fusion;
using Fusion.Photon.Realtime;
using Fusion.Sockets;
using System.Threading.Tasks;
using UnityEngine.SceneManagement;
using Projectiles;

public class NetworkManager : MonoBehaviour, INetworkRunnerCallbacks
{
    public static NetworkManager Instance { get; private set; }

    [Header("Network Settings")]
    [SerializeField] private NetworkRunner _runnerPrefab;
    [SerializeField] private NetworkSceneManagerDefault _sceneManager;

    [Header("Game Settings")]
    [SerializeField] private string _hubWorldSceneName = "SampleScene";
    [SerializeField] private string[] _gameScenes;

    private NetworkRunner _runner;
    private readonly Dictionary<string, SessionInfo> _activeSessions = new();
    private string _currentRoomCode;
    private bool _isLobbyPrivate = false;
    private readonly List<PlayerRef> _playersInLobby = new();
    private string _selectedLevelForLobby;

    public bool IsConnected => _runner != null && _runner.IsRunning;
    public bool IsHost => _runner != null && _runner.IsServer;
    public bool IsSinglePlayer => _runner != null && _runner.GameMode == GameMode.Single;

    public event Action<List<SessionInfo>> SessionListUpdated;
    public event Action<List<PlayerRef>> PlayersInLobbyUpdated;
    public event Action<bool> LobbyPrivacyChanged;
    public event Action<PlayerRef> PlayerLeftSessionEvent;
    public event Action HostEndedSessionEvent;

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(gameObject);
    }

    private void Start()
    {
        // Load the saved region or determine the best region
        InitializeRegion();

        // Initialize the runner in single player mode by default
        InitializeNetworkRunner();
    }

    /// <summary>
    /// Initializes the region by loading the saved region or determining the best region
    /// </summary>
    private async void InitializeRegion()
    {
        // Try to load the saved region
        string savedRegion = LoadSavedRegion();

        if (!string.IsNullOrEmpty(savedRegion))
        {
            // Use the saved region
            Debug.Log($"Using saved region: {savedRegion}");
            SetRegion(savedRegion);
        }
        else
        {
            // No saved region, determine the best region
            Debug.Log("No saved region found, determining best region...");
            await DetermineBestRegion();
        }
    }

    private async void InitializeNetworkRunner()
    {
        // Create initial runner but don't start a game yet
        CreateNewRunner();

        // Start in single player mode by default
        await StartSinglePlayer();

        // We'll let the UI handle session discovery when needed
        // This avoids potential issues with joining session lobbies at the wrong time
    }

    // We've removed the StartSessionDiscovery method and replaced it with SafeSessionDiscovery
    // which is only called when the player is in the OnlineOptionsPanel

    // Public method to safely discover sessions when in the OnlineOptionsPanel
    public async Task SafeSessionDiscovery()
    {
        Debug.Log("Safely discovering sessions for OnlineOptionsPanel");

        // Clear the active sessions cache to ensure we don't show stale data
        _activeSessions.Clear();

        // Only proceed if we have a valid runner in single player mode
        if (_runner != null && _runner.IsRunning && _runner.GameMode == GameMode.Single)
        {
            try
            {
                // Join the session lobby to get updates about available sessions
                await _runner.JoinSessionLobby(SessionLobby.ClientServer);
                Debug.Log("Joined session lobby for discovery");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error joining session lobby: {ex.Message}");
            }
        }
        else
        {
            Debug.Log("Cannot join session lobby - runner is not in single player mode or not running");
        }
    }

    private NetworkRunner CreateNewRunner()
    {
        // Clean up previous runner if it exists
        if (_runner != null)
        {
            if (_runner.IsRunning)
            {
                // Force cleanup ALL pooled objects when creating a new runner
                if (_runner.TryGetComponent(out NetworkObjectPool objectPool))
                {
                    // Use ForceCleanupAllObjects to ensure ALL objects are destroyed
                    objectPool.ForceCleanupAllObjects();

                    // Ensure all weapon objects are deactivated
                   // objectPool.DeactivateAllPooledObjects();
                }

                // We can't await here since this is not an async method
                // This is fine for initialization, but for game mode switches we use the async methods
                try
                {
                    // Force a synchronous shutdown
                    _runner.Shutdown(true);

                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error shutting down existing runner: {ex.Message}");
                }
            }

            try
            {
                // Destroy the runner GameObject
                Destroy(_runner.gameObject);

            }
            catch (Exception ex)
            {
                Debug.LogError($"Error destroying runner GameObject: {ex.Message}");
            }

            _runner = null;
        }

        // Create a new runner instance
        _runner = Instantiate(_runnerPrefab);
        _runner.name = "Network Runner";
        DontDestroyOnLoad(_runner.gameObject);

        // We'll let the runner initialize the scene manager when it starts
        // Don't manually initialize the scene manager here as it might not be ready yet

        // Add callbacks
        _runner.AddCallbacks(this);

        return _runner;
    }

    #region Game Mode Methods

    public async Task StartSinglePlayer()
    {
        Debug.Log("Starting single player mode");

        // Add a small delay to ensure any previous runner cleanup is complete
        await Task.Delay(100);

        // Always create a new runner for a new game session
        // This will handle cleaning up any existing runner
        CreateNewRunner();

        // Start in single player mode
        var startGameArgs = new StartGameArgs()
        {
            GameMode = GameMode.Single,
            SceneManager = _sceneManager,
            Scene = SceneRef.FromIndex(SceneManager.GetActiveScene().buildIndex),
            SessionName = "Solo Game"
        };

        // The region is already set in the global PhotonAppSettings
        if (!string.IsNullOrEmpty(_selectedRegion))
        {
            Debug.Log($"Using region {_selectedRegion} for single player mode");
        }

        try
        {
            // Make sure the runner is not null
            Debug.Assert(_runner != null, "Runner is null when trying to start single player mode");

            // The scene manager will be initialized by the runner when it starts

            var result = await _runner.StartGame(startGameArgs);
            if (result.Ok)
            {

                // Clear any player lists or other session-specific data
                _playersInLobby.Clear();

                // Make sure the object pool is properly set up for singleplayer
                if (_runner.TryGetComponent(out NetworkObjectPool objectPool))
                {
                    // Force cleanup all objects to ensure a clean state
                    objectPool.ForceCleanupAllObjects();
                }
            }
            else
            {
                Debug.LogError($"Failed to start single player mode: {result.ShutdownReason}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Exception starting single player mode: {ex.Message}");
        }
    }

    public async Task StartHosting(string roomName = "", bool isPrivate = false)
    {
        Debug.Log("Starting host mode");

        // Always create a new runner for a new game session
        // This will handle cleaning up any existing runner
        CreateNewRunner();

        // Generate a room code if none provided
        if (string.IsNullOrEmpty(roomName))
        {
            roomName = GenerateRoomCode();
        }

        _currentRoomCode = roomName;
        _isLobbyPrivate = isPrivate;
        _playersInLobby.Clear();

        // Start as host
        var startGameArgs = new StartGameArgs()
        {
            GameMode = GameMode.Host,
            SceneManager = _sceneManager,
            Scene = SceneRef.FromIndex(SceneManager.GetActiveScene().buildIndex),
            SessionName = roomName,
            IsVisible = !isPrivate // Make the room invisible if it's private
        };

        // The region is already set in the global PhotonAppSettings
        if (!string.IsNullOrEmpty(_selectedRegion))
        {
            Debug.Log($"Using region {_selectedRegion} for host mode");
        }

        try
        {
            var result = await _runner.StartGame(startGameArgs);
            if (result.Ok)
            {
                Debug.Log($"Successfully started hosting room: {roomName}, Private: {isPrivate}");

                // Add host to the players list
                if (_runner.LocalPlayer != null)
                {
                    _playersInLobby.Add(_runner.LocalPlayer);
                    PlayersInLobbyUpdated?.Invoke(_playersInLobby);
                }

                // Make sure the object pool is properly set up and all objects are deactivated
                if (_runner.TryGetComponent(out NetworkObjectPool objectPool))
                {

                    // Force cleanup all objects to ensure a clean state for hosting
                    objectPool.ForceCleanupAllObjects();
                }
            }
            else
            {
                Debug.LogError($"Failed to start hosting: {result.ShutdownReason}");
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Exception starting host mode: {ex.Message}");
        }
    }

    public async Task<(bool Success, string ErrorMessage)> JoinRoom(string roomName)
    {
        if (string.IsNullOrEmpty(roomName))
        {
            Debug.LogError("Cannot join room: Room name is null or empty");
            return (false, "Room name is empty");
        }

        Debug.Log($"Starting to join room: {roomName}");

        // Always create a new runner for a new game session
        // This will handle cleaning up any existing runner
        CreateNewRunner();

        // Store the room code for later use
        _currentRoomCode = roomName;
        _playersInLobby.Clear();

        // Join the specified room
        var startGameArgs = new StartGameArgs()
        {
            GameMode = GameMode.Client,
            SceneManager = _sceneManager,
            SessionName = roomName
        };

        // The region is already set in the global PhotonAppSettings
        if (!string.IsNullOrEmpty(_selectedRegion))
        {
            Debug.Log($"Using region {_selectedRegion} for joining room");
        }

        try
        {
            var result = await _runner.StartGame(startGameArgs);

            if (result.Ok)
            {
                Debug.Log($"Successfully joined room: {roomName}");
                return (true, string.Empty);
            }
            else
            {
                string errorMessage = "Failed to join session";

                // Check for specific error conditions
                if (result.ShutdownReason == ShutdownReason.ServerInRoom)
                {
                    errorMessage = "Session is full or no longer available";
                }
                else if (result.ShutdownReason == ShutdownReason.GameClosed)
                {
                    errorMessage = "Session has been closed by the host";
                }

                Debug.LogError($"Failed to join room: {result.ShutdownReason}");
                return (false, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Exception joining room: {ex.Message}");
            return (false, $"Error: {ex.Message}");
        }
    }

    public async Task<(bool Success, string ErrorMessage, string RoomCode)> JoinRandomRoom()
    {
        Debug.Log("Starting to join a random room");

        // Always create a new runner for a new game session
        // This will handle cleaning up any existing runner
        CreateNewRunner();

        // Clear any previous session data
        _playersInLobby.Clear();

        // Set up the arguments for joining a random room
        var startGameArgs = new StartGameArgs()
        {
            GameMode = GameMode.Client,
            // Use the built-in random matchmaking
            SessionName = null // null session name means join random
        };

        // The region is already set in the global PhotonAppSettings
        if (!string.IsNullOrEmpty(_selectedRegion))
        {
            Debug.Log($"Using region {_selectedRegion} for joining random room");
        }

        try
        {
            var result = await _runner.StartGame(startGameArgs);

            if (result.Ok)
            {
                // Successfully joined a random room
                if (_runner.SessionInfo != null)
                {
                    _currentRoomCode = _runner.SessionInfo.Name;
                    Debug.Log($"Successfully joined random room: {_currentRoomCode}");
                    return (true, string.Empty, _currentRoomCode);
                }
                else
                {
                    Debug.LogWarning("Joined a room but SessionInfo is null");
                    return (true, string.Empty, "Unknown");
                }
            }
            else
            {
                string errorMessage = "No available sessions found";

                // Check for specific error conditions
                if (result.ShutdownReason == ShutdownReason.ServerInRoom)
                {
                    errorMessage = "Session is full or no longer available";
                }

                Debug.Log($"Failed to join random room: {result.ShutdownReason}");

                await StartSinglePlayer();
                return (false, errorMessage, string.Empty);
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Exception joining random room: {ex.Message}");
            await StartSinglePlayer();

            return (false, $"Error: {ex.Message}", string.Empty);
        }
    }

    // Method to notify clients that the host is shutting down
    public void NotifyClientsOfHostShutdown()
    {
        if (_runner != null && _runner.IsServer)
        {
            Debug.Log("Host is notifying clients of shutdown");

            // Mark the session as closed to prevent new joins
            if (_runner.SessionInfo != null)
            {
                _runner.SessionInfo.IsOpen = false;

            }

            // Kick each client individually
            if (_runner.IsRunning)
            {
                // Get all connected players
                foreach (PlayerRef player in _playersInLobby)
                {
                    // Skip the host
                    if (player != _runner.LocalPlayer)
                    {
                        Debug.Log($"Kicking player {player} from session");
                        try
                        {
                            // Disconnect this specific client
                            _runner.Disconnect(player);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogError($"Error disconnecting player {player}: {ex.Message}");
                        }
                    }
                }

                // Also use the global disconnect as a backup
                try
                {
                    _runner.Disconnect(PlayerRef.None);

                }
                catch (Exception ex)
                {
                    Debug.LogError($"Error sending global disconnect: {ex.Message}");
                }

                Debug.Log("All clients have been notified of shutdown");
            }
        }
    }

    public async Task DisconnectFromRoom()
    {
        if (_runner != null && _runner.IsRunning)
        {
            try
            {
                // If we're the host, notify all clients before shutting down
                if (_runner.IsServer)
                {
                    NotifyClientsOfHostShutdown();

                    // Give a longer delay for the notification to be processed
                    // This is important to ensure clients receive the disconnect message
                    Debug.Log("Waiting for clients to process disconnect...");
                    await Task.Delay(2000);
                    Debug.Log("Proceeding with host shutdown");
                }

                // Force cleanup ALL pooled objects to prevent them from reappearing in new sessions
                if (_runner.TryGetComponent(out NetworkObjectPool objectPool))
                {

                    // Use ForceCleanupAllObjects instead of just deactivating
                    // This will destroy all objects in the pool, including borrowed ones
                    objectPool.ForceCleanupAllObjects();
                }

                // Log the current state before shutdown
                Debug.Log($"Runner state before shutdown - IsRunning: {_runner.IsRunning}, IsServer: {_runner.IsServer}");

                // Properly shutdown the runner - this will return objects to the pool
                await _runner.Shutdown(true); // Use true to ensure a clean shutdown

            }
            catch (Exception ex)
            {
                Debug.LogError($"Error during runner shutdown: {ex.Message}");
            }
            finally
            {
                // Clean up the runner GameObject after shutdown
                if (_runner != null)
                {

                    Destroy(_runner.gameObject);
                    _runner = null;
                }

                // Create a new runner and start in single player mode
                // This ensures we have a clean state for the next session

                await StartSinglePlayer();
            }
        }
    }

    public void SetRegion(string region)
    {
        try
        {
            // Store the region code for use in future connections
            _selectedRegion = region;

            // Log the region change
            Debug.Log($"Setting region to: {region}");

            // Directly modify the global PhotonAppSettings
            PhotonAppSettings.Global.AppSettings.FixedRegion = region.ToLower();
            Debug.Log($"Updated global PhotonAppSettings with region: {region}");

            // Save the region for future sessions
            SaveRegion(region);

            // If we have an active runner but it's not running, we can recreate it with the new region
            if (_runner != null && !_runner.IsRunning)
            {
                Debug.Log($"Recreating runner with region {region} for next connection");
                CreateNewRunner();
            }
            else
            {
                Debug.Log($"Region {region} will be used for the next connection");
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Failed to set region: {e.Message}");
        }
    }

    /// <summary>
    /// Saves the selected region to PlayerPrefs
    /// </summary>
    /// <param name="region">The region code to save</param>
    private void SaveRegion(string region)
    {
        try
        {
            PlayerPrefs.SetString("PhotonRegion", region);
            PlayerPrefs.Save();
            Debug.Log($"Saved region {region} to PlayerPrefs");
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error saving region to PlayerPrefs: {ex.Message}");
        }
    }

    /// <summary>
    /// Loads the saved region from PlayerPrefs
    /// </summary>
    /// <returns>The saved region code, or null if none is saved</returns>
    private string LoadSavedRegion()
    {
        try
        {
            if (PlayerPrefs.HasKey("PhotonRegion"))
            {
                string region = PlayerPrefs.GetString("PhotonRegion");
                Debug.Log($"Loaded region {region} from PlayerPrefs");
                return region;
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error loading region from PlayerPrefs: {ex.Message}");
        }

        return null;
    }

    /// <summary>
    /// Determines the best region based on ping and sets it as the current region
    /// </summary>
    private async Task DetermineBestRegion()
    {
        try
        {
            Debug.Log("Determining best region...");

            // Create a new runner for pinging regions if needed
            if (_runner == null)
            {
                CreateNewRunner();
            }

            // In Photon Fusion 2, we can't easily get the best region programmatically
            // So we'll use a predefined list of regions and default to US East

            // Add a small delay to ensure the runner is initialized
            await Task.Delay(100);

            // For now, default to US East as it's a common region with good connectivity
            string defaultRegion = "us";
            Debug.Log($"Using default region: {defaultRegion}");

            // Set the default region
            SetRegion(defaultRegion);

            // Notify the UI to update the dropdown
            GameMenuManager menuManager = FindAnyObjectByType<GameMenuManager>();
            if (menuManager != null)
            {
                // Find the index of the region in the region codes array
                int regionIndex = Array.IndexOf(menuManager.GetRegionCodes(), _selectedRegion);
                if (regionIndex >= 0)
                {
                    menuManager.SetRegionDropdownValue(regionIndex);
                }
            }
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error determining best region: {ex.Message}");

            // Default to US East if there's an error
            SetRegion("us");
        }
    }

    // Store the selected region for use when creating new runners
    private string _selectedRegion;

    /// <summary>
    /// Gets the currently selected region
    /// </summary>
    /// <returns>The selected region code, or null if none is selected</returns>
    public string GetSelectedRegion()
    {
        return _selectedRegion;
    }

    #endregion

    #region Helper Methods

    private string GenerateRoomCode()
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        var random = new System.Random();
        var code = new char[6];

        for (int i = 0; i < code.Length; i++)
        {
            code[i] = chars[random.Next(chars.Length)];
        }

        return new string(code);
    }

    public string GetCurrentRoomCode()
    {
        return _currentRoomCode;
    }

    public List<PlayerRef> GetPlayersInLobby()
    {
        return new List<PlayerRef>(_playersInLobby);
    }

    public PlayerRef GetLocalPlayerRef()
    {
        if (_runner != null && _runner.IsRunning)
        {
            return _runner.LocalPlayer;
        }
        return default;
    }

    public bool IsLobbyPrivate()
    {
        return _isLobbyPrivate;
    }

    public void SetLobbyPrivacy(bool isPrivate)
    {
        if (_isLobbyPrivate != isPrivate && _runner != null && _runner.IsServer)
        {
            _isLobbyPrivate = isPrivate;

            // Update the session properties
            if (_runner.SessionInfo != null)
            {
                // Note: In Photon Fusion 2, you might need to use a different approach to update session visibility
                // This is a placeholder - you'll need to adjust based on your specific Photon version
                Debug.Log($"Setting lobby privacy to: {isPrivate}");

                // Notify listeners about the change
                LobbyPrivacyChanged?.Invoke(isPrivate);
            }
        }
    }

    public void SetSelectedLevelForLobby(string levelName)
    {
        _selectedLevelForLobby = levelName;
    }

    public string GetSelectedLevelForLobby()
    {
        return _selectedLevelForLobby;
    }

    public string GetHubWorldSceneName()
    {
        return _hubWorldSceneName;
    }

    /// <summary>
    /// Checks if the given scene name is the hub world scene
    /// </summary>
    /// <param name="sceneName">The scene name to check</param>
    /// <returns>True if the scene is the hub world, false otherwise</returns>
    public bool IsHubWorldScene(string sceneName)
    {
        return sceneName == _hubWorldSceneName;
    }

    /// <summary>
    /// Checks if the player is currently in a game scene (not in the hub world)
    /// </summary>
    /// <returns>True if in a game scene, false if in hub world or not connected</returns>
    public bool IsInGameScene()
    {
        if (!IsConnected)
            return false;

        string currentSceneName = SceneManager.GetActiveScene().name;
        return currentSceneName != _hubWorldSceneName;
    }

    public async Task StartLobbyGame()
    {
        if (_runner != null && _runner.IsServer && !string.IsNullOrEmpty(_selectedLevelForLobby))
        {
            // Load the selected game scene
            await LoadNetworkedScene(_selectedLevelForLobby);

            // The GameModeManager will be initialized in the new scene
            // and will start the selected game mode
            Debug.Log($"Loaded game scene: {_selectedLevelForLobby}");
        }
        else
        {
            Debug.LogError("Cannot start lobby game: Not a server or no level selected");
        }
    }

    public void LoadGameScene(string sceneName)
    {
        Debug.Log($"LoadGameScene called for scene: {sceneName}");

        if (_runner != null && _runner.IsRunning)
        {
            // For Photon Fusion 2, we need to use the NetworkSceneManager to load scenes
            if (_sceneManager != null)
            {
                // Convert the scene name to a SceneRef
                // In Fusion 2, we need to get the build index and create a SceneRef from it
                int buildIndex = SceneUtility.GetBuildIndexByScenePath(sceneName);
                if (buildIndex >= 0)
                {
                    // Make sure all necessary callbacks are registered before loading the scene
                    EnsureCallbacksRegistered();

                    // Log that we're about to load the scene
                    Debug.Log($"Loading networked scene: {sceneName} (build index: {buildIndex})");

                    // Use LoadSceneMode.Single to ensure we're replacing the current scene
                    SceneRef sceneRef = SceneRef.FromIndex(buildIndex);

                    // Load the scene through the NetworkRunner
                    _runner.LoadScene(sceneRef);
                }
                else
                {
                    Debug.LogError($"Scene {sceneName} not found in build settings");
                    SceneManager.LoadScene(sceneName);
                }
            }
            else
            {
                // Fallback to regular scene loading
                Debug.LogWarning("No NetworkSceneManager found, using Unity's SceneManager instead");
                SceneManager.LoadScene(sceneName);
            }
        }
        else
        {
            Debug.LogWarning("No active NetworkRunner, using Unity's SceneManager instead");
            SceneManager.LoadScene(sceneName);
        }
    }

    public async Task LoadNetworkedScene(string sceneName)
    {
        Debug.Log($"LoadNetworkedScene called for scene: {sceneName}");

        if (_runner != null && _runner.IsRunning)
        {
            // For Photon Fusion 2, we need to use the NetworkSceneManager to load scenes
            if (_sceneManager != null)
            {
                // Convert the scene name to a SceneRef
                int buildIndex = SceneUtility.GetBuildIndexByScenePath(sceneName);
                if (buildIndex >= 0)
                {
                    // Make sure all necessary callbacks are registered
                    EnsureCallbacksRegistered();

                    // Log that we're about to load the scene
                    Debug.Log($"Loading networked scene: {sceneName} (build index: {buildIndex})");

                    // Create a SceneRef from the build index
                    SceneRef sceneRef = SceneRef.FromIndex(buildIndex);

                    // Create a TaskCompletionSource to wait for scene loading to complete
                    var tcs = new TaskCompletionSource<bool>();

                    // Set up a one-time handler for scene load completion using our existing INetworkRunnerCallbacks
                    // Store the current scene ref and task completion source for use in OnSceneLoadDone callback
                    _pendingSceneLoad = new PendingSceneLoad
                    {
                        SceneRef = sceneRef,
                        CompletionSource = tcs
                    };

                    // Load the scene - this triggers an async operation but doesn't return a Task
                    // The completion will be handled through the tcs that we set up earlier
#pragma warning disable CS4014 // Because this call is not awaited
                    _runner.LoadScene(sceneRef);
#pragma warning restore CS4014

                    // Wait for the scene load to complete via our TaskCompletionSource
                    await tcs.Task;
                }
                else
                {
                    Debug.LogError($"Scene {sceneName} not found in build settings");
                    // Fallback to regular scene loading
                    SceneManager.LoadScene(sceneName);
                }
            }
            else
            {
                // Fallback to regular scene loading
                Debug.LogWarning("No NetworkSceneManager found, using Unity's SceneManager instead");
                SceneManager.LoadScene(sceneName);
            }
        }
        else
        {
            Debug.LogWarning("No active NetworkRunner, using Unity's SceneManager instead");
            SceneManager.LoadScene(sceneName);
        }
    }

    // Helper class to track pending scene loads
    private class PendingSceneLoad
    {
        public SceneRef SceneRef;
        public TaskCompletionSource<bool> CompletionSource;
    }

    private PendingSceneLoad _pendingSceneLoad;

    /// <summary>
    /// Ensures that all necessary callbacks are registered with the NetworkRunner
    /// </summary>
    private void EnsureCallbacksRegistered()
    {
        if (_runner == null)
            return;

        // Make sure this NetworkManager is registered for callbacks
        _runner.AddCallbacks(this);

        // Find and register the GameManager if it exists
        var gameManager = FindAnyObjectByType<GameManager>();
        if (gameManager != null)
        {
            Debug.Log("Registering GameManager for NetworkRunner callbacks");
            _runner.AddCallbacks(gameManager);
        }
        else
        {
            Debug.LogWarning("GameManager not found, scene transitions may not work correctly");
        }
    }

    #endregion

    #region INetworkRunnerCallbacks Implementation

    public void OnPlayerJoined(NetworkRunner runner, PlayerRef player)
    {

        // Add player to the lobby list
        // Both host and client need to track players for UI purposes
        if (!_playersInLobby.Contains(player))
        {
            _playersInLobby.Add(player);
            PlayersInLobbyUpdated?.Invoke(_playersInLobby);
        }
    }

    public async void OnPlayerLeft(NetworkRunner runner, PlayerRef player)
    {
        // Remove player from the lobby list
        // Both host and client need to track players for UI purposes
        if (_playersInLobby.Contains(player))
        {
            _playersInLobby.Remove(player);
            PlayersInLobbyUpdated?.Invoke(_playersInLobby);
        }

        // If we're the host, notify any UI that a player left
        if (runner.IsServer)
        {
            Debug.Log($"Host detected player {player} left");
            PlayerLeftSessionEvent?.Invoke(player);
        }

        // Check if the host left (player 0 is always the host in Fusion)
        if (player.PlayerId == 0 && runner.IsClient && !runner.IsServer)
        {


            try
            {
                // Clean up the runner
                if (runner != null)
                {
                    // Shutdown the runner
                    await runner.Shutdown();


                    // Destroy the runner GameObject
                    Destroy(runner.gameObject);
                    _runner = null;
                }

                // Start single player mode
                await StartSinglePlayer();

                // Show online options panel
                GameMenuManager menuManager = FindAnyObjectByType<GameMenuManager>();
                if (menuManager != null)
                {
                    menuManager.ShowOnlineOptions();
                    Debug.Log("Returned to online options panel after host left");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error handling host departure: {ex.Message}");
                // Fallback to DisconnectFromRoom if the direct approach fails
                await DisconnectFromRoom();
            }
        }
    }

    public void OnInput(NetworkRunner runner, NetworkInput input)
    {
        // Handle input if needed
    }

    public void OnInputMissing(NetworkRunner runner, PlayerRef player, NetworkInput input)
    {
        // Handle missing input if needed
    }

    public async void OnShutdown(NetworkRunner runner, ShutdownReason shutdownReason)
    {
        Debug.Log($"Network runner shutdown: {shutdownReason}");

        // If this is a client and the shutdown reason is related to the host disconnecting
        if (runner.IsClient && !runner.IsServer &&
            (shutdownReason == ShutdownReason.HostMigration ||
             shutdownReason == ShutdownReason.ServerInRoom ||
             shutdownReason == ShutdownReason.GameClosed ||
             shutdownReason == ShutdownReason.Error))
        {
            Debug.Log("Client detected host shutdown. Starting single player mode.");

            try
            {
                // Clean up the network object pool first
                if (runner.TryGetComponent(out NetworkObjectPool objectPool))
                {
                    // Force cleanup all objects to ensure a clean state
                    objectPool.ForceCleanupAllObjects();
                    objectPool.DeactivateAllPooledObjects();
                }

                // Clean up the runner
                if (runner != null)
                {
                    Destroy(runner.gameObject);
                    if (runner == _runner)
                    {
                        _runner = null;
                    }
                }

                // Add a small delay to ensure resources are released
                await Task.Delay(200);

                // Start single player mode
                await StartSinglePlayer();

                // Show online options panel
                GameMenuManager menuManager = FindAnyObjectByType<GameMenuManager>();
                if (menuManager != null)
                {
                    menuManager.ShowOnlineOptions();
                    Debug.Log("Returned to online options panel after host shutdown");
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error handling shutdown: {ex.Message}");
                // Try to recover by starting single player mode directly after a longer delay
                await Task.Delay(500);
                _ = StartSinglePlayer();
            }
        }
    }

    // These are Fusion callbacks
    void INetworkRunnerCallbacks.OnConnectedToServer(NetworkRunner runner)
    {
        Debug.Log("Connected to server");

        // If we're a client, add ourselves to the local players list
        if (runner.IsClient && !runner.IsServer)
        {
            if (!_playersInLobby.Contains(runner.LocalPlayer))
            {
                _playersInLobby.Add(runner.LocalPlayer);
                PlayersInLobbyUpdated?.Invoke(_playersInLobby);
            }

            // Get the session name if available
            if (runner.SessionInfo != null && !string.IsNullOrEmpty(runner.SessionInfo.Name))
            {
                _currentRoomCode = runner.SessionInfo.Name;
                Debug.Log($"Connected to session: {_currentRoomCode}");
            }
        }
    }

    async void INetworkRunnerCallbacks.OnDisconnectedFromServer(NetworkRunner runner, NetDisconnectReason reason)
    {
        Debug.Log($"Disconnected from server: {reason}");

        // Clear the active sessions list to ensure we don't show stale sessions
        _activeSessions.Clear();

        // If we're a client and we get disconnected from the server (host left or closed the session)
        if (runner.IsClient && !runner.IsServer)
        {
            Debug.Log("Host has left or closed the session. Returning to single player mode.");

            // Notify any UI that might be listening that the host ended the session
            HostEndedSessionEvent?.Invoke();

            try
            {
                // Clean up the network object pool first
                if (runner.TryGetComponent(out NetworkObjectPool objectPool))
                {
                    // Force cleanup all objects to ensure a clean state
                    objectPool.ForceCleanupAllObjects();
                    objectPool.DeactivateAllPooledObjects();
                }

                // Properly shutdown the runner before destroying it
                if (runner.IsRunning)
                {
                    try
                    {
                        await runner.Shutdown(true);
                    }
                    catch (Exception shutdownEx)
                    {
                        Debug.LogError($"Error during runner shutdown: {shutdownEx.Message}");
                    }
                }

                // Clean up the runner
                if (runner != null)
                {
                    Destroy(runner.gameObject);
                    if (runner == _runner)
                    {
                        _runner = null;
                    }
                }

                // Add a small delay to ensure resources are released
                await Task.Delay(200);

                // Start single player mode
                await StartSinglePlayer();

                // Show online options panel
                GameMenuManager menuManager = FindAnyObjectByType<GameMenuManager>();
                if (menuManager != null)
                {
                    menuManager.ShowOnlineOptions();
                }
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error handling disconnection: {ex.Message}");
                // Try to recover by starting single player mode directly after a longer delay
                await Task.Delay(500);
                _ = StartSinglePlayer();
            }

            // Note: The GameMenuManager is already handled in the try block above
        }
    }

    public void OnConnectRequest(NetworkRunner runner, NetworkRunnerCallbackArgs.ConnectRequest request, byte[] token)
    {
        // Accept or reject connection requests
        request.Accept();
    }

    public void OnConnectFailed(NetworkRunner runner, NetAddress remoteAddress, NetConnectFailedReason reason)
    {
        Debug.LogError($"Connect failed: {reason}");
    }

    public void OnUserSimulationMessage(NetworkRunner runner, SimulationMessagePtr message)
    {
        // Handle user simulation messages
    }

    public void OnSessionListUpdated(NetworkRunner runner, List<SessionInfo> sessionList)
    {
        _activeSessions.Clear();
        foreach (var session in sessionList)
        {
            _activeSessions[session.Name] = session;
        }

        SessionListUpdated?.Invoke(sessionList);
        Debug.Log($"Session list updated: {sessionList.Count} sessions available");

        // Log details about available sessions for debugging
        foreach (var session in sessionList)
        {
            Debug.Log($"Available session: {session.Name}, Players: {session.PlayerCount}/{session.MaxPlayers}, IsVisible: {session.IsVisible}, IsOpen: {session.IsOpen}");
        }
    }

    public void OnCustomAuthenticationResponse(NetworkRunner runner, Dictionary<string, object> data)
    {
        // Handle custom authentication
    }

    public void OnHostMigration(NetworkRunner runner, HostMigrationToken hostMigrationToken)
    {
        // Handle host migration
    }

    public void OnReliableDataReceived(NetworkRunner runner, PlayerRef player, ReliableKey key, ArraySegment<byte> data)
    {
        // Handle reliable data
    }

    public void OnReliableDataProgress(NetworkRunner runner, PlayerRef player, ReliableKey key, float progress)
    {
        // Handle reliable data progress
    }

    public void OnSceneLoadDone(NetworkRunner runner)
    {
        Debug.Log("Scene load done");

        // Check if we have a pending scene load to complete
        if (_pendingSceneLoad != null)
        {
            // Complete the task
            Debug.Log($"Completing pending scene load for scene ref: {_pendingSceneLoad.SceneRef}");
            _pendingSceneLoad.CompletionSource.TrySetResult(true);
            _pendingSceneLoad = null;
        }

        // If we're the server and in a game scene, start the game mode
        if (runner.IsServer && IsInGameScene())
        {
            // Find the GameModeManager in the scene
            var gameModeManager = runner.SimulationUnityScene.GetComponent<GameModeManager>(true);
            if (gameModeManager != null)
            {
                // Start the game
                Debug.Log("Starting game mode");
                gameModeManager.StartGame();
            }
            else
            {
                Debug.LogWarning("GameModeManager not found in scene");
            }
        }
    }

    public void OnSceneLoadStart(NetworkRunner runner)
    {
        Debug.Log("Scene load started - cleaning up pooled objects");

        // Get the NetworkObjectPool and clean up all objects for the scene transition
        if (runner.TryGetComponent(out NetworkObjectPool objectPool))
        {
            // Call our new method to ensure all objects are properly destroyed during scene transition
            objectPool.CleanupForSceneTransition();
        }
    }

    public void OnObjectEnterAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player)
    {
        // Called when an object enters a player's area of interest
    }

    public void OnObjectExitAOI(NetworkRunner runner, NetworkObject obj, PlayerRef player)
    {
        // Called when an object exits a player's area of interest
    }

    #endregion
}
