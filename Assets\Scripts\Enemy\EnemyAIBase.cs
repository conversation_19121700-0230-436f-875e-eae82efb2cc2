using UnityEngine;
using Fusion;
using UnityEngine.AI;
using Projectiles;
using System.Collections;
using Unity.VisualScripting;
using System;

public class EnemyAIBase : ContextBehaviour
{
    [SerializeField]
    private NavMeshAgent _navMeshAgent;
    [Header("Movement")]
    [SerializeField]
    private float moveSpeed = 2f;

    [Header("Attack")]
    [SerializeField]
    private float attackRange = 2f;
    [SerializeField]
    private float attackInterval = 1f;
    [SerializeField]
    private float attackDamage = 10f;

    [Header("Health and Hit Reaction")]
    [SerializeField]
    private Health health;
    [SerializeField]
    private EnemyMeshController _enemyMeshController;
    [SerializeField]
    private float _receviedHitMovementStopTime = 2f;

    private HitboxRoot _hitboxRoot;
    [Networked]
    private TickTimer _receviedHitCooldown { get; set; }
    private float _DespawnTime = 5f;
    [Networked]
    private TickTimer _despawnTimer { get; set; }

    public event Action<NetworkObject> OnDeath; // Event for enemy death

    private Transform _currentTarget;
    private NavMeshPath _cachedPath;
    private int _pathCheckInterval = 10;
    private int _frameCounter = 0;
    private bool _isAttacking = false;

    private void Awake()
    {
        Debug.Assert(_navMeshAgent, "ASSIGN NavMeshAgent TO EnemyAIBase COMPONENT ON " + this.gameObject);
        Debug.Assert(health, "ASSIGN health TO EnemyAIBase COMPONENT ON " + this.gameObject);
        Debug.Assert(_enemyMeshController, "ASSIGN enemyMeshController TO EnemyAIBase COMPONENT ON " + this.gameObject);

        _navMeshAgent.enabled = false;
        _cachedPath = new NavMeshPath();

        // Get the HitboxRoot component
        _hitboxRoot = GetComponent<HitboxRoot>();
        if (_hitboxRoot == null)
        {
            Debug.LogWarning($"No HitboxRoot found on {gameObject.name}");
        }
        else
        {
            Debug.Log($"<color=purple>HitboxRoot found on {gameObject.name} with {_hitboxRoot.Hitboxes?.Length ?? 0} hitboxes registered</color>");

            // Register all enemy hitboxes with the HitboxRoot if they're not already registered
            if (_hitboxRoot.Hitboxes == null || _hitboxRoot.Hitboxes.Length == 0)
            {
                RegisterHitboxesWithRoot();
            }
        }
    }

    private void RegisterHitboxesWithRoot()
    {
        if (_hitboxRoot == null || _enemyMeshController == null)
            return;

        // Get all hitboxes from the enemy mesh controller
        var allHitboxes = new System.Collections.Generic.List<Hitbox>();

        // Add hitboxes from all arrays
        if (_enemyMeshController.headHitBoxes != null)
            allHitboxes.AddRange(_enemyMeshController.headHitBoxes);
        if (_enemyMeshController.bodyHitBox != null)
            allHitboxes.AddRange(_enemyMeshController.bodyHitBox);
        if (_enemyMeshController.rightArmHitBox != null)
            allHitboxes.AddRange(_enemyMeshController.rightArmHitBox);
        if (_enemyMeshController.leftArmHitBox != null)
            allHitboxes.AddRange(_enemyMeshController.leftArmHitBox);
        if (_enemyMeshController.rightLegHitBox != null)
            allHitboxes.AddRange(_enemyMeshController.rightLegHitBox);
        if (_enemyMeshController.leftLegHitBox != null)
            allHitboxes.AddRange(_enemyMeshController.leftLegHitBox);

        // Remove null entries
        allHitboxes.RemoveAll(h => h == null);

        if (allHitboxes.Count > 0)
        {
            // Use reflection to set the Hitboxes array since it might be private
            var hitboxesField = typeof(HitboxRoot).GetField("Hitboxes",
                System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (hitboxesField != null)
            {
                hitboxesField.SetValue(_hitboxRoot, allHitboxes.ToArray());
                Debug.Log($"<color=green>Registered {allHitboxes.Count} hitboxes with HitboxRoot for {gameObject.name}</color>");
            }
            else
            {
                Debug.LogWarning($"Could not find Hitboxes field on HitboxRoot for {gameObject.name}");
            }
        }
    }

    // MONOBEHAVIOUR

    protected void OnEnable()
    {
        health.HitTaken += OnHitTaken;
        health.FatalHitTaken += OnFatalTaken;
    }

    protected void OnDisable()
    {

        health.HitTaken -= OnHitTaken;
        health.FatalHitTaken -= OnFatalTaken;
    }

    public override void Spawned()
    {
        _navMeshAgent.speed = moveSpeed;
        _navMeshAgent.autoRepath = true;
        _navMeshAgent.enabled = true;

        _enemyMeshController.ResetMesh();

        // Only reset immortality cooldown, let Fusion handle the rest of the health state
        if (HasStateAuthority)
        {
            health.StopImmortality();
            Debug.Log($"<color=green>Cleared immortality cooldown for {gameObject.name} on spawn</color>");
        }

        // Use HitboxRoot for proper network synchronization like players do
        if (_hitboxRoot != null && _hitboxRoot.Hitboxes != null && _hitboxRoot.Hitboxes.Length > 0)
        {
            Debug.Log($"<color=green>Using HitboxRoot with {_hitboxRoot.Hitboxes.Length} hitboxes for {gameObject.name}</color>");
            // HitboxRoot will be managed in FixedUpdateNetwork like players
        }
        else
        {
            Debug.LogWarning($"HitboxRoot not properly set up for {gameObject.name}, falling back to manual hitbox management");
            // Fallback to manual hitbox management
            if (HasStateAuthority)
            {
                StartCoroutine(EnableHitboxesAfterNetworkSync());
            }
        }
        Debug.Log($"<color=blue>Enemy {gameObject.name} spawned - Health: {health.CurrentHealth}, IsAlive: {health.IsAlive}, HasStateAuthority: {HasStateAuthority}</color>");
    }

    private System.Collections.IEnumerator EnableHitboxesAfterNetworkSync()
    {
        // Wait a few frames to ensure network state is fully synchronized
        yield return new WaitForFixedUpdate();
        yield return new WaitForFixedUpdate();

        Debug.Log($"<color=green>Enabling hitboxes after network sync for {gameObject.name}</color>");
        _enemyMeshController.UpdateHitboxState(true);
    }

    public override void FixedUpdateNetwork()
    {
        // Use HitboxRoot for proper network synchronization like players do
        if (_hitboxRoot != null && _hitboxRoot.Hitboxes != null && _hitboxRoot.Hitboxes.Length > 0)
        {
            _hitboxRoot.HitboxRootActive = health.IsAlive;
        }
        else
        {
            // Fallback to manual hitbox management if HitboxRoot is not set up
            if (HasStateAuthority)
            {
                _enemyMeshController.UpdateHitboxState(health.IsAlive);
            }
        }

        if (!Object.HasStateAuthority) return;


        if(health.IsAlive)
        {
            _frameCounter++;
            if (_frameCounter % _pathCheckInterval != 0) return;

            if (_navMeshAgent.isStopped)
            {
                if(_receviedHitCooldown.Expired(Runner))
                {
                    _navMeshAgent.isStopped = false;
                }
            }
            //Do movement and pathfinding logic
            else
            {
                Transform closestPlayer = GetClosestPlayer();
                Transform closestDefence = GetClosestDefence();
                if (closestPlayer == null) return;
                if (IsPathValid(closestPlayer.position))
                {
                    _currentTarget = closestPlayer;
                }
                else if (closestDefence != null)
                {
                    _currentTarget = closestDefence;
                }
                if (_currentTarget != null)
                {
                    float distanceToTarget = Vector3.Distance(transform.position, _currentTarget.position);

                    if (distanceToTarget <= attackRange)
                    {
                        if (!_isAttacking)
                        {
                            _isAttacking = true;
                            StartCoroutine(AttackTarget());
                        }
                    }
                    else
                    {
                        _navMeshAgent.SetDestination(_currentTarget.position);
                        _isAttacking = false;
                    }
                }
            }



        }
        else
        {//Is dead
            _receviedHitCooldown = default;

            if (_despawnTimer.Expired(Runner))
            {
                DespawnEnemy();
            }

        }


    }

    private bool IsPathValid(Vector3 targetPosition)
    {
        _navMeshAgent.CalculatePath(targetPosition, _cachedPath);
        return _cachedPath.status == NavMeshPathStatus.PathComplete;
    }

    private Transform GetClosestPlayer()
    {
        Transform closestPlayer = null;
        float closestDistanceSqr = float.MaxValue;
        Vector3 currentPosition = transform.position;

        foreach (var kvp in Context.Gameplay.Players)
        {
            var player = kvp.Value;
            if (player == null || player.ActiveAgent == null) continue;

            Transform playerTransform = player.ActiveAgent.GetPlayerBodyTransform().transform;
            float distanceSqr = (playerTransform.position - currentPosition).sqrMagnitude;

            if (distanceSqr < closestDistanceSqr)
            {
                closestDistanceSqr = distanceSqr;
                closestPlayer = playerTransform;
            }
        }

        return closestPlayer;
    }

    private Transform GetClosestDefence()
    {
        Transform closestDefence = null;
        float closestDistanceSqr = float.MaxValue;
        Vector3 currentPosition = transform.position;

        var defences = Context.Gameplay.DefencePrototypes;
        for (int i = 0; i < defences.Length; i++)
        {
            DefencePrototype defence = defences[i];
            if (defence == null) continue;

            Transform defenceTransform = defence.transform;
            float distanceSqr = (defenceTransform.position - currentPosition).sqrMagnitude;

            if (distanceSqr < closestDistanceSqr)
            {
                closestDistanceSqr = distanceSqr;
                closestDefence = defenceTransform;
            }
        }

        return closestDefence;
    }

    private IEnumerator AttackTarget()
    {
        while (_currentTarget != null && Vector3.Distance(transform.position, _currentTarget.position) <= attackRange)
        {
            ApplyHitDamage(_currentTarget);
            yield return new WaitForSeconds(attackInterval);
        }

        _isAttacking = false;
    }

    private void ApplyHitDamage(Transform target)
    {
        Hitbox hitbox = target.GetComponent<Hitbox>();
        Collider collider = target.GetComponent<Collider>();
        if (hitbox != null) // Fusion Hitbox Detected
        {
            LagCompensatedHit hitData = new LagCompensatedHit
            {
                Hitbox = hitbox,
                Collider = hitbox.GetComponent<Collider>(),
                Point = target.position,
                Normal = Vector3.up
            };

            HitUtility.ProcessHit(Object.InputAuthority, transform.forward, hitData, attackDamage, EHitType.Enemy);
        }
        else if (collider != null) // Fallback to Unity Collider
        {
            LagCompensatedHit hitData = new LagCompensatedHit
            {
                Collider = collider,
                Point = target.position,
                Normal = Vector3.up
            };

            HitUtility.ProcessHit(Object.InputAuthority, transform.forward, hitData, attackDamage, EHitType.Enemy);
        }
    }


    private void OnHitTaken(HitData hitData)
    {
        //Appyl damage visuals
        _enemyMeshController.DoLimbDamage(hitData);




        //Think only server needs to make adjustments to movement logic
        if (HasStateAuthority)
        {
            //Create cool down for preventing enemy from moving
            _receviedHitCooldown = TickTimer.CreateFromSeconds(Runner, _receviedHitMovementStopTime);
            _navMeshAgent.isStopped = true;
        }


    }
    private void OnFatalTaken(HitData hitData)
    {

        Die();
    }

    private void Die()
    {
        _enemyMeshController.SetIsDead();
        _receviedHitCooldown = default;
        _navMeshAgent.enabled = false;
        if (HasStateAuthority)
        {
            _despawnTimer = TickTimer.CreateFromSeconds(Runner, _DespawnTime);
        }

        // Notify listeners (e.g., wave system) that this enemy has died
        OnDeath?.Invoke(Object);
    }

    private void DespawnEnemy()
    {
        _despawnTimer = default;
        Runner.Despawn(Object);
    }

}
