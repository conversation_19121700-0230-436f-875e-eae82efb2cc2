using UnityEngine;
using System.Collections.Generic;
/// <summary>
/// Creates a default wave configuration asset that can be used with game modes.
/// This script is meant to be run in the editor to create the asset.
/// </summary>
#if UNITY_EDITOR
public class DefaultWaveConfig : MonoBehaviour
{
    [ContextMenu("Create Default Wave Config")]
    public void CreateDefaultWaveConfig()
    {
        // Create a new WaveConfig asset
        WaveConfig waveConfig = ScriptableObject.CreateInstance<WaveConfig>();

        // Configure the asset
        waveConfig.isEndless = false;
        waveConfig.endlessStartWave = 5;
        waveConfig.globalDifficultyMultiplier = 1.0f;

        // Add some default waves
        for (int i = 0; i < 10; i++)
        {
            WaveConfig.WaveInfo wave = new WaveConfig.WaveInfo
            {
                waveName = $"Wave {i + 1}",
                baseEnemyCount = 5 + (i * 3),
                enemyCountMultiplier = 1.0f + (i * 0.1f),
                timeBetweenSpawns = Mathf.Max(0.5f, 1.0f - (i * 0.05f)),
                cooldownAfterWave = Mathf.Max(10f, 20f - (i * 1f)),
                difficultyIncrease = 0.1f,
                enemies = new List<WaveConfig.EnemySpawnInfo>()
            };

            // Add a default enemy type
            wave.enemies.Add(new WaveConfig.EnemySpawnInfo
            {
                enemyName = "Basic Enemy",
                spawnWeight = 1.0f,
                difficultyScaling = 0.1f
            });

            // Add the wave to the config
            waveConfig.waves.Add(wave);
        }

        // Save the asset
        UnityEditor.AssetDatabase.CreateAsset(waveConfig, "Assets/Resources/DefaultWaveConfig.asset");
        UnityEditor.AssetDatabase.SaveAssets();

        Debug.Log("Created default wave config at Assets/Resources/DefaultWaveConfig.asset");
    }
}
#endif
