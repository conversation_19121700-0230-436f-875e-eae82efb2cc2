using UnityEngine;
using Fusion;
/// <summary>
/// Component that applies team colors to player agents.
/// </summary>
public class TeamColorApplier : NetworkBehaviour
{
    [SerializeField] private Renderer[] renderersToColor;
    [SerializeField] private int materialIndex = 0;

    [Networked] private Color NetworkedColor { get; set; }

    private void OnEnable()
    {
        // Apply the current color when enabled
        ApplyColorToRenderers(NetworkedColor);
    }

    public void ApplyColor(Color color)
    {
        if (!HasStateAuthority) return;

        // Set the networked color
        NetworkedColor = color;

        // Apply the color immediately on the server
        ApplyColorToRenderers(color);
    }

    public override void Render()
    {
        // Apply the color during rendering
        ApplyColorToRenderers(NetworkedColor);
    }

    private void ApplyColorToRenderers(Color color)
    {
        if (renderersToColor == null || renderersToColor.Length == 0) return;

        foreach (var renderer in renderersToColor)
        {
            if (renderer != null)
            {
                // Check if the material index is valid
                if (materialIndex >= 0 && materialIndex < renderer.materials.Length)
                {
                    // Create a copy of the materials array
                    Material[] materials = renderer.materials;

                    // Set the color on the specified material
                    materials[materialIndex].color = color;

                    // Apply the modified materials back to the renderer
                    renderer.materials = materials;
                }
            }
        }
    }
}