fileFormatVersion: 2
guid: 555fd5d618b459948bb907a30851c7bd
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable: []
  externalObjects: {}
  materials:
    materialImportMode: 2
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: "\nClip 'Take 001' has import animation warnings that
      might lower retargeting quality:\nNote: Activate translation DOF on avatar
      to improve retargeting quality.\n\t'Gnome_Chest_Bone' has translation animation
      that will be discarded.\n\t'Gnome_Chest_Bone' is inbetween humanoid transforms
      and has rotation animation that will be discarded.\n"
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: TestWalk
      takeName: Take 001
      internalID: 1827226128182048838
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Gnome_Waist_Bone
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Leg_Top_Bone_1
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Leg_Top_Bone_1
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Leg_Bottom_Bone_1
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Leg_Bottom_Bone_1
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Foot_Bone_1
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Foot_Bone_1
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Spine_Bone_1
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Neck_Bone
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Head_Bone_1
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Shoulder_Bone
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Shoulder_Bone
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Upper_Arm_Bone
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Upper_Arm_Bone
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Forearm_Bone
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Forearm_Bone
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Arm_Wrist_Bone
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Arm_Wrist_Bone
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Toe_Bone_1
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Toe_Bone_1
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Head_Bone_2
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Thumb_Bone_1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Thumb_Bone_2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Thumb_Bone_3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Hand_Bone
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Finger_Bone_1
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Left_Finger_Bone_2
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Thumb_Bone_1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Thumb_Bone_2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Thumb_Bone_3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Hand_Bone
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Finger_Bone_1
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Gnome_Right_Finger_Bone_2
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Gnome_Rigged(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome
      parentName: Gnome_Rigged(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: locator1
      parentName: Gnome_Rigged(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Base_Nurb_1
      parentName: locator1
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Base_Nurb_2
      parentName: Gnome_Base_Nurb_1
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Waist_Nurb_2
      parentName: Gnome_Base_Nurb_2
      position: {x: 5.475221e-20, y: 0.49684152, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Waist_Nurb_1
      parentName: Gnome_Waist_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Spine_Nurb_1
      parentName: Gnome_Waist_Nurb_1
      position: {x: -3.8857806e-18, y: 0.21500415, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Chest_Nurb_2
      parentName: Gnome_Spine_Nurb_1
      position: {x: 1.6653345e-18, y: 0.21439917, z: 4.2370459e-35}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Chest_Nurb_1
      parentName: Gnome_Chest_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Neck_Nurb_2
      parentName: Gnome_Chest_Nurb_1
      position: {x: -6.661338e-18, y: 0.22324729, z: 9.860761e-34}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Neck_Nurb_1
      parentName: Gnome_Neck_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Head_Nurb_2
      parentName: Gnome_Neck_Nurb_1
      position: {x: 6.661338e-18, y: 0.14021744, z: 1.4744919e-32}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Head_Nurb_1
      parentName: Gnome_Head_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Shoulder_Nurb
      parentName: Gnome_Chest_Nurb_1
      position: {x: -0.25840816, y: 0.07714134, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Hand_Nurb_2
      parentName: Gnome_Left_Shoulder_Nurb
      position: {x: -0.5497173, y: 0.0030691528, z: 6.661338e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Hand_Nurb_1
      parentName: Gnome_Left_Hand_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Finger_Nurb_1
      parentName: Gnome_Left_Hand_Nurb_1
      position: {x: -0.17528129, y: -0.0009460449, z: -0.0061274515}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Finger_Nurb_2
      parentName: Gnome_Left_Finger_Nurb_1
      position: {x: -0.05207115, y: -0.004806747, z: -0.00062736333}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Finger_Nurb_3
      parentName: Gnome_Left_Finger_Nurb_2
      position: {x: -0.056462705, y: -0.0077251433, z: -0.0012547267}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Thumb_Nurb_1
      parentName: Gnome_Left_Hand_Nurb_1
      position: {x: -0.076785274, y: -0.0007738495, z: 0.061575927}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Thumb_Nurb_2
      parentName: Gnome_Left_Thumb_Nurb_1
      position: {x: -0.02169342, y: -0.002018509, z: 0.03180782}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Thumb_Nurb_3
      parentName: Gnome_Left_Thumb_Nurb_2
      position: {x: -0.024964904, y: -0.003763809, z: 0.020732125}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle1
      parentName: Gnome_Left_Hand_Nurb_1
      position: {x: 2.842171e-16, y: -4.2632563e-16, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle2
      parentName: ikHandle1
      position: {x: -0.108153425, y: -0.0009460825, z: -0.004872725}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_L_Elbow_Locator
      parentName: Gnome_Left_Shoulder_Nurb
      position: {x: -0.30945486, y: 0.0030691528, z: -0.79999995}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 10, y: 10, z: 10}
    - name: Gnome_Right_Shoulder_Nurb
      parentName: Gnome_Chest_Nurb_1
      position: {x: 0.25840798, y: 0.07714508, z: -3.9443044e-33}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Hand_Nurb_2
      parentName: Gnome_Right_Shoulder_Nurb
      position: {x: 0.549717, y: 0.0030700683, z: 7.105427e-17}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Hand_Nurb_1
      parentName: Gnome_Right_Hand_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Thumb_Nurb_1
      parentName: Gnome_Right_Hand_Nurb_1
      position: {x: 0.076785736, y: -0.0007785034, z: 0.061575927}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Thumb_Nurb_2
      parentName: Gnome_Right_Thumb_Nurb_1
      position: {x: 0.02169342, y: -0.002018509, z: 0.03180782}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Thumb_Nurb_3
      parentName: Gnome_Right_Thumb_Nurb_2
      position: {x: 0.024964904, y: -0.003763809, z: 0.020732125}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Finger_Nurb_1
      parentName: Gnome_Right_Hand_Nurb_1
      position: {x: 0.17528175, y: -0.00095069886, z: -0.0061274515}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Finger_Nurb_2
      parentName: Gnome_Right_Finger_Nurb_1
      position: {x: 0.05207115, y: -0.004806747, z: -0.00062736333}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Finger_Nurb_3
      parentName: Gnome_Right_Finger_Nurb_2
      position: {x: 0.056462705, y: -0.0077251433, z: -0.0012547267}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle3
      parentName: Gnome_Right_Hand_Nurb_1
      position: {x: -0, y: -0.000000037231445, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle4
      parentName: ikHandle3
      position: {x: 0.108154, y: -0.00094999996, z: -0.00487273}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_R_Elbow_Locator
      parentName: Gnome_Right_Shoulder_Nurb
      position: {x: 0.309455, y: 0.0030700683, z: -0.79999995}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 10, y: 10, z: 10}
    - name: Gnome_R_Knee_Locator
      parentName: Gnome_Waist_Nurb_1
      position: {x: 0.192073, y: -0.21032955, z: 0.6982381}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 10, y: 10, z: 10}
    - name: Gnome_L_Knee_Locator
      parentName: Gnome_Waist_Nurb_1
      position: {x: -0.19207269, y: -0.21032967, z: 0.6982381}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 10, y: 10, z: 10}
    - name: Gnome_Right_Foot_Nurb_2
      parentName: Gnome_Base_Nurb_2
      position: {x: 0.19162999, y: 0.146728, z: -4.440892e-18}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Right_Foot_Nurb_1
      parentName: Gnome_Right_Foot_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle7
      parentName: Gnome_Right_Foot_Nurb_1
      position: {x: -0.0000000010681153, y: -6.408692e-10, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle8
      parentName: ikHandle7
      position: {x: 3.5527136e-17, y: -0.1119734, z: 0.218395}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Foot_Nurb_2
      parentName: Gnome_Base_Nurb_2
      position: {x: -0.19163026, y: 0.14672779, z: -4.440892e-18}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Left_Foot_Nurb_1
      parentName: Gnome_Left_Foot_Nurb_2
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle5
      parentName: Gnome_Left_Foot_Nurb_1
      position: {x: 0.000000007957658, y: -0.000000003892768, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: ikHandle6
      parentName: ikHandle5
      position: {x: -0, y: -0.11197316, z: 0.21839546}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Gnome_Waist_Bone
      parentName: locator1
      position: {x: 5.475221e-20, y: 0.49684152, z: 0}
      rotation: {x: -0, y: -0, z: -0.7040786, w: 0.71012205}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: Gnome_Spine_Bone_1
      parentName: Gnome_Waist_Bone
      position: {x: -0.2149963, y: 0.0018375752, z: 0}
      rotation: {x: -0, y: -0, z: 0.0027683973, w: 0.9999962}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: Gnome_Chest_Bone
      parentName: Gnome_Spine_Bone_1
      position: {x: -0.21437791, y: 0.0030194074, z: 4.2370459e-35}
      rotation: {x: -0.99979955, y: 0.020024508, z: 1.2261499e-18, w: -6.122007e-17}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000002}
    - name: Gnome_Neck_Bone
      parentName: Gnome_Chest_Bone
      position: {x: -0.22317202, y: 0.005796676, z: 7.0988806e-19}
      rotation: {x: 0.99970984, y: -0.024091076, z: -1.4751528e-18, w: 6.1214555e-17}
      scale: {x: 1.0000002, y: 1, z: 0.9999998}
    - name: Gnome_Head_Bone_1
      parentName: Gnome_Neck_Bone
      position: {x: -0.14018282, y: 0.003115174, z: -6.436859e-20}
      rotation: {x: -3.3087218e-23, y: 1.1994116e-23, z: -0.011109129, w: 0.9999383}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: Gnome_Head_Bone_2
      parentName: Gnome_Head_Bone_1
      position: {x: -0.22641052, y: 2.94717e-16, z: -1.0393662e-19}
      rotation: {x: 1.7746164e-24, y: -8.490278e-24, z: 0.70710677, w: 0.7071067}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: Gnome_Left_Shoulder_Bone
      parentName: Gnome_Chest_Bone
      position: {x: -0.0704057, y: 0.26032403, z: 3.1880497e-17}
      rotation: {x: 0.6949727, y: -0.71903616, z: -4.402827e-17, w: 4.2554836e-17}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Gnome_Left_Upper_Arm_Bone
      parentName: Gnome_Left_Shoulder_Bone
      position: {x: -0.12470736, y: 0.004074957, z: -1.2257308e-25}
      rotation: {x: -0.00027399132, y: 0.04008187, z: -0.014887597, w: 0.9990854}
      scale: {x: 0.99999994, y: 0.9999998, z: 1}
    - name: Gnome_Left_Forearm_Bone
      parentName: Gnome_Left_Upper_Arm_Bone
      position: {x: -0.18467505, y: -0.0040146755, z: -0.02971117}
      rotation: {x: 0.0008202093, y: -0.06302192, z: 0.008704562, w: 0.9979739}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: Gnome_Left_Arm_Wrist_Bone
      parentName: Gnome_Left_Forearm_Bone
      position: {x: -0.2402602, y: -0.0010311597, z: 0.02971117}
      rotation: {x: 1.4475462e-15, y: -5.761195e-17, z: 0.0021458527, w: 0.99999774}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: Gnome_Left_Hand_Bone
      parentName: Gnome_Left_Arm_Wrist_Bone
      position: {x: -0.108153455, y: -0.0009460072, z: -0.004872725}
      rotation: {x: 7.0576633e-10, y: -0.037514586, z: 0.000000023981553, w: 0.9992961}
      scale: {x: 1, y: 0.99999994, z: 1}
    - name: Gnome_Left_Finger_Bone_1
      parentName: Gnome_Left_Hand_Bone
      position: {x: -0.06703298, y: 0.000000047082462, z: 0.003781805}
      rotation: {x: -0.00014820154, y: 0.02512921, z: -0.0047977637, w: 0.9996728}
      scale: {x: 0.99999994, y: 1, z: 0.9999999}
    - name: Gnome_Left_Finger_Bone_2
      parentName: Gnome_Left_Finger_Bone_1
      position: {x: -0.052071154, y: -0.0048067262, z: 0.00062718114}
      rotation: {x: 2.5102062e-10, y: 0.012045567, z: -0.0000000058207648, w: 0.99992746}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: Gnome_Left_Finger_Bone_3
      parentName: Gnome_Left_Finger_Bone_2
      position: {x: -0.056462698, y: -0.0077250954, z: -0.0012547267}
      rotation: {x: -0, y: 9.3132224e-10, z: 1.1666011e-16, w: 1}
      scale: {x: 0.9999998, y: 1, z: 1}
    - name: Gnome_Left_Thumb_Bone_1
      parentName: Gnome_Left_Arm_Wrist_Bone
      position: {x: -0.07678527, y: -0.0007738036, z: 0.061575927}
      rotation: {x: -0.000000013620592, y: 0.4093111, z: 0.0000000058207648, w: 0.91239494}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
    - name: Gnome_Left_Thumb_Bone_2
      parentName: Gnome_Left_Thumb_Bone_1
      position: {x: -0.038182065, y: -0.0020184878, z: 0.004946977}
      rotation: {x: -0.011437559, y: -0.02451982, z: -0.022329189, w: 0.9993845}
      scale: {x: 1, y: 1, z: 1.0000002}
    - name: Gnome_Left_Thumb_Bone_3
      parentName: Gnome_Left_Thumb_Bone_2
      position: {x: -0.03227217, y: -0.0037638114, z: 0.0034021342}
      rotation: {x: 0.000000006693881, y: -0.28978413, z: -0.00000004819594, w: 0.9570921}
      scale: {x: 0.9999997, y: 1, z: 1}
    - name: Gnome_Right_Shoulder_Bone
      parentName: Gnome_Chest_Bone
      position: {x: -0.08382873, y: -0.2563178, z: -3.1389873e-17}
      rotation: {x: -4.214029e-17, y: 4.290162e-17, z: 0.7134083, w: -0.7007486}
      scale: {x: 1, y: 1.0000004, z: 1}
    - name: Gnome_Right_Upper_Arm_Bone
      parentName: Gnome_Right_Shoulder_Bone
      position: {x: 0.12470719, y: -0.0040758713, z: -3.004214e-17}
      rotation: {x: -0.0002739727, y: 0.040081885, z: -0.014887508, w: 0.9990855}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000005}
    - name: Gnome_Right_Forearm_Bone
      parentName: Gnome_Right_Upper_Arm_Bone
      position: {x: 0.18467537, y: 0.0040146667, z: 0.029711196}
      rotation: {x: 0.0008202055, y: -0.06302224, z: 0.008704443, w: 0.9979739}
      scale: {x: 1, y: 1.0000004, z: 1.0000005}
    - name: Gnome_Right_Arm_Wrist_Bone
      parentName: Gnome_Right_Forearm_Bone
      position: {x: 0.24025978, y: 0.0010310457, z: -0.029711207}
      rotation: {x: -0.00000002105121, y: -4.5177452e-11, z: 0.0021459719, w: 0.99999774}
      scale: {x: 0.99999994, y: 1.0000004, z: 1.0000005}
    - name: Gnome_Right_Hand_Bone
      parentName: Gnome_Right_Arm_Wrist_Bone
      position: {x: 0.10815399, y: 0.0009499335, z: 0.0048727356}
      rotation: {x: -0.0000000024140652, y: -0.03751458, z: -0.000000035248657, w: 0.99929607}
      scale: {x: 1, y: 1.0000005, z: 1.0000004}
    - name: Gnome_Right_Finger_Bone_1
      parentName: Gnome_Right_Hand_Bone
      position: {x: 0.067033134, y: -0.00000004120067, z: -0.0037818193}
      rotation: {x: -0.0001490939, y: 0.02512726, z: -0.004826763, w: 0.9996726}
      scale: {x: 0.99999994, y: 1.0000002, z: 1.0000002}
    - name: Gnome_Right_Finger_Bone_2
      parentName: Gnome_Right_Finger_Bone_1
      position: {x: 0.052073, y: 0.00481, z: -0.0006272289}
      rotation: {x: 6.017958e-10, y: 0.012045571, z: -0.000000005567193, w: 0.99992746}
      scale: {x: 1, y: 1.0000007, z: 1.0000007}
    - name: Gnome_Right_Finger_Bone_3
      parentName: Gnome_Right_Finger_Bone_2
      position: {x: 0.056459997, y: 0.007723, z: 0.00125473}
      rotation: {x: 5.5511138e-17, y: -0, z: -1.0711915e-16, w: 1}
      scale: {x: 0.99999994, y: 1.0000001, z: 1}
    - name: Gnome_Right_Thumb_Bone_1
      parentName: Gnome_Right_Arm_Wrist_Bone
      position: {x: 0.076786, y: 0.0007799548, z: -0.061575893}
      rotation: {x: -0.000000014902708, y: 0.409311, z: 0.0000000069866575, w: 0.9123949}
      scale: {x: 1.0000001, y: 1, z: 1}
    - name: Gnome_Right_Thumb_Bone_2
      parentName: Gnome_Right_Thumb_Bone_1
      position: {x: 0.038181834, y: 0.00202, z: -0.0049473513}
      rotation: {x: -0.011429626, y: -0.024523396, z: -0.02228316, w: 0.99938554}
      scale: {x: 1.0000005, y: 1, z: 0.99999994}
    - name: Gnome_Right_Thumb_Bone_3
      parentName: Gnome_Right_Thumb_Bone_2
      position: {x: 0.03227229, y: 0.003761, z: -0.0034021428}
      rotation: {x: -0.000000007225802, y: -0.28978413, z: 0.000000070966635, w: 0.95709205}
      scale: {x: 1.0000001, y: 1, z: 0.99999934}
    - name: Gnome_Left_Leg_Top_Bone_1
      parentName: Gnome_Waist_Bone
      position: {x: 0.09410313, y: -0.19146045, z: 0}
      rotation: {x: 0.000038846396, y: 0.020240072, z: 0.99974024, w: 0.010481894}
      scale: {x: 1.0000005, y: 1.0000004, z: 1}
    - name: Gnome_Left_Leg_Bottom_Bone_1
      parentName: Gnome_Left_Leg_Top_Bone_1
      position: {x: -0.11460246, y: 0.000000024046003, z: 0.02341872}
      rotation: {x: 0.9999697, y: 0.007793315, z: 1.382432e-10, w: -0.0000000037252903}
      scale: {x: 1.0000001, y: 0.9999998, z: 1.0000001}
    - name: Gnome_Left_Foot_Bone_1
      parentName: Gnome_Left_Leg_Bottom_Bone_1
      position: {x: -0.13978478, y: -0.0000000341681, z: 0.023418717}
      rotation: {x: -0.6148372, y: -0.37710875, z: -0.3602746, w: 0.5915797}
      scale: {x: 1.0000004, y: 1, z: 1}
    - name: Gnome_Left_Toe_Bone_1
      parentName: Gnome_Left_Foot_Bone_1
      position: {x: -0.24526808, y: 0, z: 0}
      rotation: {x: -0.16593827, y: -0.6873606, z: -0.16593824, w: 0.6873605}
      scale: {x: 0.9999997, y: 0.9999997, z: 1}
    - name: Gnome_Right_Leg_Top_Bone_1
      parentName: Gnome_Waist_Bone
      position: {x: 0.09736147, y: 0.18982379, z: 0}
      rotation: {x: 0.99979305, y: 0.0019371212, z: 0.00013421569, w: -0.020250183}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: Gnome_Right_Leg_Bottom_Bone_1
      parentName: Gnome_Right_Leg_Top_Bone_1
      position: {x: 0.11460285, y: -0.0000004900624, z: -0.023418691}
      rotation: {x: -0.99996966, y: -0.007793461, z: 0.0000000011605152, w: 0.0000000049821445}
      scale: {x: 1, y: 1.0000001, z: 0.99999976}
    - name: Gnome_Right_Foot_Bone_1
      parentName: Gnome_Right_Leg_Bottom_Bone_1
      position: {x: 0.1397847, y: -0.0000005272043, z: -0.0234187}
      rotation: {x: 0.6148434, y: 0.37711266, z: 0.3602705, w: -0.5915732}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: Gnome_Right_Toe_Bone_1
      parentName: Gnome_Right_Foot_Bone_1
      position: {x: 0.24526761, y: 0, z: 0}
      rotation: {x: -0.16593835, y: -0.6873605, z: -0.16593826, w: 0.6873605}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 54f83a70ae800af47acb58fedcda5277, type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  importBlendShapeDeformPercent: 1
  remapMaterialsIfMaterialImportModeIsNone: 0
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
