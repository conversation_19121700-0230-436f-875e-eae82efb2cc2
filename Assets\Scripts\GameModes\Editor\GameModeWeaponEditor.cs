#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;

namespace GameModes.Editor
{
    [CustomEditor(typeof(GameModeBase), true)]
    public class GameModeWeaponEditor : UnityEditor.Editor
    {
        private SerializedProperty startingRightWeaponSlot;
        private SerializedProperty startingLeftWeaponSlot;
        private SerializedProperty isEndless;
        private SerializedProperty maxWaves;
        private SerializedProperty difficultyMultiplier;

        private void OnEnable()
        {
            startingRightWeaponSlot = serializedObject.FindProperty("startingRightWeaponSlot");
            startingLeftWeaponSlot = serializedObject.FindProperty("startingLeftWeaponSlot");
            isEndless = serializedObject.FindProperty("isEndless");
            maxWaves = serializedObject.FindProperty("maxWaves");
            difficultyMultiplier = serializedObject.FindProperty("difficultyMultiplier");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();

            // Draw default inspector excluding our custom properties
            DrawPropertiesExcluding(serializedObject, "startingRightWeaponSlot", "startingLeftWeaponSlot");

            // Draw weapon selection section
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Starting Weapons", EditorStyles.boldLabel);
            
            EditorGUILayout.PropertyField(startingRightWeaponSlot, new GUIContent("Right Hand Weapon Slot", "The weapon slot to equip in the player's right hand (0 = empty hand)"));
            EditorGUILayout.PropertyField(startingLeftWeaponSlot, new GUIContent("Left Hand Weapon Slot", "The weapon slot to equip in the player's left hand (0 = empty hand)"));
            
            EditorGUILayout.HelpBox("Weapon Slots: 0 = Empty Hand, 1-11 = Available Weapons", MessageType.Info);

            serializedObject.ApplyModifiedProperties();
        }
    }
}
#endif
