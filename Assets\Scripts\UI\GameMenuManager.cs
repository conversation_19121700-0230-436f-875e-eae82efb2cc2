using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Fusion;

public class GameMenuManager : MonoBehaviour
{
    [Header("UI Panels")]
    [SerializeField] private GameObject _mainMenuPanel;
    [SerializeField] private GameObject _soloLevelSelectPanel;
    [SerializeField] private GameObject _onlineOptionsPanel;
    [SerializeField] private GameObject _lobbyPanel;
    [SerializeField] private NetworkStatusPanel _networkStatusPanel;

    [Header("Main Menu")]
    [SerializeField] private Button _playSoloButton;
    [SerializeField] private Button _playOnlineButton;

    [Header("Solo Level Select")]
    [SerializeField] private Button _soloBackButton;
    [SerializeField] private Button _soloPlayButton;
    [SerializeField] private TMP_Dropdown _soloLevelDropdown;

    [Header("Online Options")]
    [SerializeField] private Button _hostGameButton;
    [SerializeField] private Button _joinRandomButton;
    [SerializeField] private Button _joinByCodeButton;
    [SerializeField] private Button _onlineBackButton;
    [SerializeField] private TMP_InputField _roomCodeInput;
    [SerializeField] private Transform _friendsListContainer;
    [SerializeField] private GameObject _friendSessionPrefab;
    [SerializeField] private TMP_Dropdown _regionDropdown;

    // Region data
    private readonly string[] _regionCodes = { "us", "usw", "eu", "asia", "jp", "au", "in", "za", "sa" };
    private readonly string[] _regionNames = { "US East", "US West", "Europe", "Asia", "Japan", "Australia", "India", "South Africa", "South America" };

    [Header("Game Levels")]
    [SerializeField] private List<GameLevelInfo> _gameLevels = new();

    [Header("Lobby Panel")]
    [SerializeField] private TextMeshProUGUI _lobbyRoomCodeText;
    [SerializeField] private Button _lobbyStartGameButton;
    [SerializeField] private Button _lobbyBackButton;
    [SerializeField] private Button _lobbyInviteFriendsButton;
    [SerializeField] private Toggle _lobbyPrivateToggle;
    [SerializeField] private TMP_Dropdown _lobbyLevelDropdown;
    [SerializeField] private Transform _lobbyPlayersContainer;
    [SerializeField] private GameObject _lobbyPlayerItemPrefab;

    private NetworkManager _networkManager;
    private string _selectedLevel;
    private List<SessionInfo> _currentSessions = new();

    [System.Serializable]
    public class GameLevelInfo
    {
        public string levelName;
        public string sceneName;
    }

    private void Awake()
    {
        // Make sure all panels are hidden at start
        _mainMenuPanel.SetActive(false);
        _soloLevelSelectPanel.SetActive(false);
        _onlineOptionsPanel.SetActive(false);
        _lobbyPanel.SetActive(false);
    }

    private void Start()
    {
        // Get reference to NetworkManager
        _networkManager = NetworkManager.Instance;
        if (_networkManager == null)
        {
            Debug.LogError("NetworkManager instance not found!");
            return;
        }

        // Check if NetworkStatusPanel is assigned
        if (_networkStatusPanel != null)
        {

        }
        else
        {
            Debug.LogWarning("NetworkStatusPanel not assigned in inspector");
        }

        // Subscribe to session list updates and network events
        _networkManager.SessionListUpdated += OnSessionListUpdated;
        _networkManager.PlayersInLobbyUpdated += OnPlayersInLobbyUpdated;
        _networkManager.PlayerLeftSessionEvent += OnPlayerLeftSession;
        _networkManager.HostEndedSessionEvent += OnHostEndedSession;
        _networkManager.LobbyPrivacyChanged += OnLobbyPrivacyChanged;

        // Setup button listeners
        SetupButtonListeners();

        // Populate dropdowns
        PopulateLevelDropdown();
        PopulateRegionDropdown();

        // Check if we're returning to the hubworld from a game scene
        // and we're still in an active session
        if (CheckForActiveSession())
        {
            ShowLobbyPanel();
            Debug.Log("Showing lobby panel for active session");
        }
        else
        {
            // Show main menu
            ShowMainMenu();
        }
    }

    private void OnDestroy()
    {
        if (_networkManager != null)
        {
            _networkManager.SessionListUpdated -= OnSessionListUpdated;
            _networkManager.PlayersInLobbyUpdated -= OnPlayersInLobbyUpdated;
            _networkManager.PlayerLeftSessionEvent -= OnPlayerLeftSession;
            _networkManager.HostEndedSessionEvent -= OnHostEndedSession;
            _networkManager.LobbyPrivacyChanged -= OnLobbyPrivacyChanged;
        }
    }

    private void OnEnable()
    {
        // Subscribe to scene loaded event
        UnityEngine.SceneManagement.SceneManager.sceneLoaded += OnSceneLoaded;
    }

    private void OnDisable()
    {
        // Unsubscribe from scene loaded event
        UnityEngine.SceneManagement.SceneManager.sceneLoaded -= OnSceneLoaded;
    }

    private void OnSceneLoaded(UnityEngine.SceneManagement.Scene scene, UnityEngine.SceneManagement.LoadSceneMode mode)
    {
        // Check if this is the hubworld scene and we're in an active session
        if (_networkManager != null && scene.name == _networkManager.GetHubWorldSceneName())
        {
            Debug.Log("Hubworld scene loaded, checking for active session");

            // Wait a frame to ensure all objects are properly initialized
            StartCoroutine(CheckSessionAfterSceneLoad());
        }
    }

    private System.Collections.IEnumerator CheckSessionAfterSceneLoad()
    {
        // Wait for a frame to ensure everything is initialized
        yield return null;

        // Check if we're in an active session
        if (CheckForActiveSession())
        {
            Debug.Log("Active session detected after scene load, showing lobby panel");
            ShowLobbyPanel();
        }
    }

    #region UI Setup

    private void SetupButtonListeners()
    {
        // Main Menu
        _playSoloButton.onClick.AddListener(OnPlaySoloClickedAsync);
        _playOnlineButton.onClick.AddListener(OnPlayOnlineClicked);

        // Solo Level Select
        _soloBackButton.onClick.AddListener(OnSoloBackClicked);
        _soloPlayButton.onClick.AddListener(OnSoloPlayClicked);
        _soloLevelDropdown.onValueChanged.AddListener(OnSoloLevelSelected);

        // Online Options
        _hostGameButton.onClick.AddListener(OnHostGameClicked);
        _joinRandomButton.onClick.AddListener(OnJoinRandomClicked);
        _joinByCodeButton.onClick.AddListener(OnJoinByCodeClicked);
        _onlineBackButton.onClick.AddListener(OnOnlineBackClicked);
        if (_regionDropdown != null)
        {
            _regionDropdown.onValueChanged.AddListener(OnRegionDropdownChanged);
        }

        // Lobby Panel
        _lobbyStartGameButton.onClick.AddListener(OnLobbyStartGameClicked);
        _lobbyBackButton.onClick.AddListener(OnLobbyBackClicked);
        _lobbyInviteFriendsButton.onClick.AddListener(OnLobbyInviteFriendsClicked);
        _lobbyPrivateToggle.onValueChanged.AddListener(OnLobbyPrivacyToggled);
        _lobbyLevelDropdown.onValueChanged.AddListener(OnLobbyLevelSelected);
    }

    private void PopulateLevelDropdown()
    {
        // Clear both dropdowns
        _soloLevelDropdown.ClearOptions();
        _lobbyLevelDropdown.ClearOptions();

        List<TMP_Dropdown.OptionData> options = new();
        foreach (var level in _gameLevels)
        {
            options.Add(new TMP_Dropdown.OptionData(level.levelName));
        }

        // Add options to both dropdowns
        _soloLevelDropdown.AddOptions(options);
        _lobbyLevelDropdown.AddOptions(options);

        if (options.Count > 0)
        {
            _selectedLevel = _gameLevels[0].sceneName;
            if (_networkManager != null)
            {
                _networkManager.SetSelectedLevelForLobby(_gameLevels[0].sceneName);
            }
        }
    }

    private void PopulateRegionDropdown()
    {
        if (_regionDropdown == null) return;

        // Clear dropdown
        _regionDropdown.ClearOptions();

        // Add region options
        List<TMP_Dropdown.OptionData> options = new();
        foreach (var regionName in _regionNames)
        {
            options.Add(new TMP_Dropdown.OptionData(regionName));
        }

        _regionDropdown.AddOptions(options);

        // If the network manager has a selected region, use that
        if (_networkManager != null && !string.IsNullOrEmpty(_networkManager.GetSelectedRegion()))
        {
            string selectedRegion = _networkManager.GetSelectedRegion();
            int regionIndex = Array.IndexOf(_regionCodes, selectedRegion);
            if (regionIndex >= 0)
            {
                _regionDropdown.value = regionIndex;
            }
            else
            {
                // Default to first region if the selected region is not in the list
                _regionDropdown.value = 0;
                if (_regionCodes.Length > 0)
                {
                    _networkManager.SetRegion(_regionCodes[0]);
                }
            }
        }
        else
        {
            // Default to first region
            _regionDropdown.value = 0;
            if (_networkManager != null && _regionCodes.Length > 0)
            {
                _networkManager.SetRegion(_regionCodes[0]);
            }
        }
    }

    /// <summary>
    /// Gets the array of region codes
    /// </summary>
    /// <returns>The array of region codes</returns>
    public string[] GetRegionCodes()
    {
        return _regionCodes;
    }

    /// <summary>
    /// Sets the value of the region dropdown
    /// </summary>
    /// <param name="index">The index to set</param>
    public void SetRegionDropdownValue(int index)
    {
        if (_regionDropdown != null && index >= 0 && index < _regionCodes.Length)
        {
            _regionDropdown.value = index;
        }
    }

    #endregion

    #region UI Navigation

    public void ShowMainMenu()
    {
        _mainMenuPanel.SetActive(true);
        _soloLevelSelectPanel.SetActive(false);
        _onlineOptionsPanel.SetActive(false);
        _lobbyPanel.SetActive(false);
    }

    private void ShowSoloLevelSelect()
    {
        _mainMenuPanel.SetActive(false);
        _soloLevelSelectPanel.SetActive(true);
        _onlineOptionsPanel.SetActive(false);
        _lobbyPanel.SetActive(false);
    }

    public async void ShowOnlineOptions()
    {
        _mainMenuPanel.SetActive(false);
        _soloLevelSelectPanel.SetActive(false);
        _onlineOptionsPanel.SetActive(true);
        _lobbyPanel.SetActive(false);

        // Only try to discover sessions if we have a valid NetworkManager
        if (_networkManager != null)
        {
            // Use the safe session discovery method
            await _networkManager.SafeSessionDiscovery();
        }

        // Refresh session list UI
        UpdateFriendsList();
    }

    // Check if we're in an active multiplayer session
    private bool CheckForActiveSession()
    {
        if (_networkManager == null) return false;

        // Check if we're connected to a network session
        if (!_networkManager.IsConnected) return false;

        // Check if we're in single player mode
        if (_networkManager.IsSinglePlayer) return false;

        // Check if we have a valid room code
        string roomCode = _networkManager.GetCurrentRoomCode();
        if (string.IsNullOrEmpty(roomCode)) return false;

        // Check if there are players in the lobby
        List<PlayerRef> players = _networkManager.GetPlayersInLobby();
        if (players == null || players.Count == 0) return false;

        // We're in an active multiplayer session
        Debug.Log($"Active session detected with {players.Count} players and room code {roomCode}");
        return true;
    }

    private void ShowLobbyPanel()
    {
        _mainMenuPanel.SetActive(false);
        _soloLevelSelectPanel.SetActive(false);
        _onlineOptionsPanel.SetActive(false);
        _lobbyPanel.SetActive(true);

        // Update lobby UI
        UpdateLobbyUI();
    }

    private void UpdateLobbyUI()
    {
        if (_networkManager == null) return;

        // Update room code
        string roomCode = _networkManager.GetCurrentRoomCode();
        if (_lobbyRoomCodeText != null)
        {
            _lobbyRoomCodeText.text = $"Room Code: {roomCode}";
        }

        // Update privacy toggle
        if (_lobbyPrivateToggle != null)
        {
            _lobbyPrivateToggle.isOn = _networkManager.IsLobbyPrivate();
        }

        // Update player list
        UpdateLobbyPlayersList();

        // Enable/disable start button based on host status
        if (_lobbyStartGameButton != null)
        {
            _lobbyStartGameButton.interactable = _networkManager.IsHost;
        }
    }

    #endregion

    #region Button Handlers

    private void OnPlaySoloClickedAsync()
    {
       // await _networkManager.StartSinglePlayer();
        ShowSoloLevelSelect();
    }

    private void OnPlayOnlineClicked()
    {
        ShowOnlineOptions();
    }

    private void OnSoloBackClicked()
    {
        ShowMainMenu();
    }

    private async void OnSoloPlayClicked()
    {
        if (string.IsNullOrEmpty(_selectedLevel))
        {
            Debug.LogWarning("No level selected");
            return;
        }

        // Make sure we're in single player mode
        await _networkManager.StartSinglePlayer();

        // Load the selected level
        _networkManager.LoadGameScene(_selectedLevel);
    }

    private void OnSoloLevelSelected(int index)
    {
        if (index >= 0 && index < _gameLevels.Count)
        {
            _selectedLevel = _gameLevels[index].sceneName;
        }
    }

    private async void OnHostGameClicked()
    {
        bool isPrivate = false; // Default to public lobby

        // Show status panel while connecting
        if (_networkStatusPanel != null)
        {
            _networkStatusPanel.ShowStatus("Creating Session", "Connecting to online servers...");
        }

        await _networkManager.StartHosting(string.Empty, isPrivate);

        // Show room code if available
        string roomCode = _networkManager.GetCurrentRoomCode();
        if (!string.IsNullOrEmpty(roomCode))
        {
            Debug.Log($"Room code: {roomCode}");

            // Update status panel with room code before hiding
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.UpdateMessage("Session created successfully!");
                _networkStatusPanel.UpdateRoomCode(roomCode);
                _networkStatusPanel.HideStatus();
            }

            // Show the lobby panel
            ShowLobbyPanel();
        }
        else
        {
            // If something went wrong, show error
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.ShowStatus("Error", "Failed to create session", "", 3f);
            }
        }
    }

    private async void OnJoinRandomClicked()
    {
        // Show status panel while searching
        if (_networkStatusPanel != null)
        {
            _networkStatusPanel.ShowStatus("Joining Game", "Searching for available sessions...");
        }

        var (success, errorMessage, roomCode) = await _networkManager.JoinRandomRoom();

        // After joining, show the lobby panel
        if (success)
        {
            // Update status panel with room code before hiding
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.UpdateMessage("Session found! Connecting...");
                _networkStatusPanel.UpdateRoomCode(roomCode);

                // Hide status after a short delay
                await Task.Delay(1000);
                _networkStatusPanel.HideStatus();
            }

            ShowLobbyPanel();
        }
        else
        {
            // If something went wrong, show error
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.ShowStatus("Error", errorMessage, "", 3f);
            }
        }
    }

    private async void OnJoinByCodeClicked()
    {
        string roomCode = _roomCodeInput.text.Trim().ToUpper();
        if (string.IsNullOrEmpty(roomCode))
        {
            Debug.LogWarning("Room code is empty");
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.ShowStatus("Error", "Please enter a room code", "", 3f);
            }
            return;
        }

        // Show status panel while connecting
        if (_networkStatusPanel != null)
        {
            _networkStatusPanel.ShowStatus("Joining Session", $"Connecting to session...", roomCode);
        }

        var (success, errorMessage) = await _networkManager.JoinRoom(roomCode);

        // After joining, show the lobby panel
        if (success)
        {
            // Hide status panel
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.HideStatus();
            }
            ShowLobbyPanel();
        }
        else
        {
            // If something went wrong, show error with the specific message
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.ShowStatus("Error", errorMessage, "", 3f);
            }
        }
    }

    private void OnOnlineBackClicked()
    {
        ShowMainMenu();
    }

    private void OnRegionDropdownChanged(int index)
    {
        if (_networkManager != null && index >= 0 && index < _regionCodes.Length)
        {
            _networkManager.SetRegion(_regionCodes[index]);
            Debug.Log($"Region changed to: {_regionNames[index]} ({_regionCodes[index]})");
        }
    }

    #endregion

    #region Session Management

    private void OnSessionListUpdated(List<SessionInfo> sessionList)
    {
        Debug.Log($"UI received session list update: {sessionList.Count} sessions available");

        // Filter out any sessions that are not open or visible
        var filteredList = sessionList.FindAll(s => s.IsOpen && s.IsVisible);
        Debug.Log($"After filtering: {filteredList.Count} valid sessions");

        _currentSessions = filteredList;

        // Only update the UI if the OnlineOptionsPanel is active
        if (_onlineOptionsPanel.activeSelf)
        {
            UpdateFriendsList();
        }
    }

    private void OnPlayersInLobbyUpdated(List<PlayerRef> players)
    {
        UpdateLobbyPlayersList();
    }

    private void OnLobbyPrivacyChanged(bool isPrivate)
    {
        if (_lobbyPrivateToggle != null)
        {
            _lobbyPrivateToggle.isOn = isPrivate;
        }
    }

    private void OnPlayerLeftSession(PlayerRef player)
    {
        // Show a notification when a player leaves the session
        if (_networkStatusPanel != null && _lobbyPanel.activeSelf)
        {
            _networkStatusPanel.ShowStatus("Player Left", $"Player {player} has left the session", "", 3f);
        }
    }

    private void OnHostEndedSession()
    {
        // Show a notification when the host ends the session
        if (_networkStatusPanel != null)
        {
            _networkStatusPanel.ShowStatus("Session Ended", "The host has ended the session", "", 3f);
        }
    }

    private void UpdateFriendsList()
    {
        // Clear existing items
        foreach (Transform child in _friendsListContainer)
        {
            Destroy(child.gameObject);
        }

        // Add session items
        foreach (var session in _currentSessions)
        {
            GameObject sessionObj = Instantiate(_friendSessionPrefab, _friendsListContainer);
            Button button = sessionObj.GetComponent<Button>();
            TextMeshProUGUI text = sessionObj.GetComponentInChildren<TextMeshProUGUI>();

            text.text = $"{session.Name} ({session.PlayerCount}/{session.MaxPlayers})";
            string sessionName = session.Name;

            button.onClick.AddListener(async () => {

                 // Show a notification when the host ends the session
        if (_networkStatusPanel != null)
         _networkStatusPanel.ShowStatus("Connecting", "Joining Session...", sessionName);

                await _networkManager.JoinRoom(sessionName);
                // After joining, show the lobby panel
                ShowLobbyPanel();

                 if (_networkStatusPanel != null)
                 _networkStatusPanel.HideStatus();




            });
        }
    }

    private void UpdateLobbyPlayersList()
    {
        if (_networkManager == null || _lobbyPlayersContainer == null) return;

        // Clear existing items
        foreach (Transform child in _lobbyPlayersContainer)
        {
            Destroy(child.gameObject);
        }

        // Get players in lobby
        List<PlayerRef> players = _networkManager.GetPlayersInLobby();

        // Add player items
        foreach (var player in players)
        {
            GameObject playerObj = Instantiate(_lobbyPlayerItemPrefab, _lobbyPlayersContainer);

            // Try to get the LobbyPlayerItem component using TryGetComponent to avoid allocation
            if (playerObj.TryGetComponent(out LobbyPlayerItem lobbyPlayerItem))
            {
                // Use the LobbyPlayerItem component to initialize the player item
                PlayerRef localPlayer = _networkManager.GetLocalPlayerRef();
                bool isLocalPlayer = player == localPlayer;
                bool isHost = _networkManager.IsHost && player.PlayerId == 0; // Player 0 is always the host in Fusion
                lobbyPlayerItem.Initialize(player, isHost);
            }
            else
            {
                // Fallback to using TextMeshProUGUI directly
                TextMeshProUGUI text = playerObj.GetComponentInChildren<TextMeshProUGUI>();
                if (text != null)
                {
                    // Display player info
                    PlayerRef localPlayer = _networkManager.GetLocalPlayerRef();
                    bool isLocalPlayer = player == localPlayer;
                    bool isHost = _networkManager.IsHost && player.PlayerId == 0; // Player 0 is always the host in Fusion
                    text.text = isHost ? $"Player {player} (Host)" : $"Player {player}";
                }
            }
        }
    }

    #endregion

    #region Lobby Panel Handlers

    private async void OnLobbyStartGameClicked()
    {
        if (_networkManager != null && _networkManager.IsHost)
        {
            await _networkManager.StartLobbyGame();
        }
    }

    private async void OnLobbyBackClicked()
    {
        if (_networkManager != null)
        {
            // Show appropriate message based on whether we're host or client
            if (_networkStatusPanel != null)
            {
                if (_networkManager.IsHost)
                {
                    _networkStatusPanel.ShowStatus("Closing Session", "Notifying all players and shutting down...");
                }
                else
                {
                    _networkStatusPanel.ShowStatus("Leaving Session", "Disconnecting from session...");
                }
            }

            // Disconnect from the room (this will handle both host and client cases)
            await _networkManager.DisconnectFromRoom();

            // Hide status panel
            if (_networkStatusPanel != null)
            {
                _networkStatusPanel.HideStatus();
            }

            // Return to the online options panel
            ShowOnlineOptions();
        }
    }

    private void OnLobbyInviteFriendsClicked()
    {
        // Copy room code to clipboard for sharing
        if (_networkManager != null)
        {
            string roomCode = _networkManager.GetCurrentRoomCode();
            if (!string.IsNullOrEmpty(roomCode))
            {
                GUIUtility.systemCopyBuffer = roomCode;
                Debug.Log($"Room code copied to clipboard: {roomCode}");

                // You could show a notification here
            }
        }
    }

    private void OnLobbyPrivacyToggled(bool isPrivate)
    {
        if (_networkManager != null && _networkManager.IsHost)
        {
            _networkManager.SetLobbyPrivacy(isPrivate);
        }
    }

    private void OnLobbyLevelSelected(int index)
    {
        if (_networkManager != null && _networkManager.IsHost && index >= 0 && index < _gameLevels.Count)
        {
            _networkManager.SetSelectedLevelForLobby(_gameLevels[index].sceneName);
        }
    }

    #endregion
}



