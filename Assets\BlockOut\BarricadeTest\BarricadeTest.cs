using UnityEngine;

public class BarricadeTest : MonoBehaviour
{
    float Opaque = 1;
    float Transparent = 0.4f;
    bool isTransparent = false;
    public KeyCode Button;

    Material mat;

    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        mat = GetComponent<MeshRenderer>().material;
    }

    // Update is called once per frame
    void Update()
    {
        if (Input.GetKeyDown(Button))
        {
            if(!isTransparent)
            {
                isTransparent = true;
                mat.SetFloat("_Opacity", Transparent);
            }
            else
            {
                isTransparent = false;
                mat.SetFloat("_Opacity", Opaque);
            }
        }
    }
}
