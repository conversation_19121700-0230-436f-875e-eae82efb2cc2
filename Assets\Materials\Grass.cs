using UnityEngine;

public class Grass : MonoBehaviour
{
    private MeshRenderer MR;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Start()
    {
        MR = GetComponent<MeshRenderer>();
        int randomNumber = Random.Range(1, 9);
        MR.material.SetFloat("_Random", randomNumber);
    }

    // Update is called once per frame
    void Update()
    {
        
    }
}
