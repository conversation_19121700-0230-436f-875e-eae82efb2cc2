using System.Collections.Generic;
using UnityEngine;
using Fusion;

namespace Projectiles
{
	public class NetworkObjectPool : Fusion.Behaviour, INetworkObjectProvider
	{
		public SceneContext Context { get; set; }

		private Dictionary<NetworkPrefabId, Stack<NetworkObject>> _cached   = new(32);
		private Dictionary<NetworkObject, NetworkPrefabId> _borrowed = new();

		NetworkObjectAcquireResult INetworkObjectProvider.AcquirePrefabInstance(NetworkRunner runner, in NetworkPrefabAcquireContext context, out NetworkObject result)
		{
			// Initialize result to null
			result = null;

			try
			{
				if (_cached.TryGetValue(context.PrefabId, out var objects) == false)
				{
					objects = _cached[context.PrefabId] = new Stack<NetworkObject>();
				}

				if (objects.Count > 0)
				{
					// Try to get a cached instance
					NetworkObject oldInstance = null;

					// Pop objects until we find a valid one or the stack is empty
					while (objects.Count > 0 && oldInstance == null)
					{
						oldInstance = objects.Pop();

						// Check if the object is still valid
						if (oldInstance == null || !oldInstance)
						{
							// Object was destroyed, skip it
							oldInstance = null;
							continue;
						}
					}

					// If we found a valid cached instance, use it
					if (oldInstance != null)
					{
						_borrowed[oldInstance] = context.PrefabId;

						#if UNITY_EDITOR
							var originalPrefab = runner.Config.PrefabTable.Load(context.PrefabId, true);
							if (originalPrefab != null)
							{
								oldInstance.name = originalPrefab.name;
							}
						#endif

						oldInstance.SetActive(true);

						result = oldInstance;
						return NetworkObjectAcquireResult.Success;
					}
				}

			// Need to create a new instance
			var original = runner.Config.PrefabTable.Load(context.PrefabId, true);
			if (original == null)
			{
				result = default;
				return NetworkObjectAcquireResult.Failed;
			}

			try
			{
				// Create a new instance
				var instance = Instantiate(original);
				runner.MoveToRunnerScene(instance.gameObject);

				#if UNITY_EDITOR
					instance.name = original.name;
				#endif

				_borrowed[instance] = context.PrefabId;

				// Assign context to the instance and its nested objects
				AssignContext(instance);

				if (instance.NestedObjects != null)
				{
					for (int i = 0; i < instance.NestedObjects.Length; i++)
					{
						if (instance.NestedObjects[i] != null)
						{
							AssignContext(instance.NestedObjects[i]);
						}
					}
				}

				result = instance;
				return NetworkObjectAcquireResult.Success;
			}
			catch (System.Exception ex)
			{
				Debug.LogError($"Error creating instance: {ex.Message}");
				result = default;
				return NetworkObjectAcquireResult.Failed;
			}
		}
		catch (System.Exception ex)
		{
			Debug.LogError($"Error in AcquirePrefabInstance: {ex.Message}");
			result = default;
			return NetworkObjectAcquireResult.Failed;
		}
		}

		void INetworkObjectProvider.ReleaseInstance(NetworkRunner runner, in NetworkObjectReleaseContext context)
		{
			if (context.IsNestedObject == true)
				return;



			NetworkObject instance = context.Object;
			if (instance == null)
				return;

			// Check if this is a player object or a weapon object
			bool isPlayerObject = IsPlayerObject(instance);
			// bool isWeaponObject = IsWeaponOrSpecialObject(instance);


			if (instance.NetworkTypeId.IsSceneObject == false && runner.IsShutdown == false)
			{
				// If it's a player object, destroy it completely
				// We don't want to pool player objects as they're unique per session
				if (isPlayerObject)
				{

					// Remove from borrowed if it's there
					if (_borrowed.ContainsKey(instance))
					{
						_borrowed.Remove(instance);
					}

					// Destroy the object
					Destroy(instance.gameObject);
				}
				// For weapons and other objects, return them to the pool
				else if (_borrowed.TryGetValue(instance, out var prefabID))
				{
					_borrowed.Remove(instance);
					_cached[prefabID].Push(instance);

					// Make sure the object is deactivated
					instance.SetActive(false);
					instance.transform.parent = null;
					instance.transform.SetPositionAndRotation(Vector3.zero, Quaternion.identity);

					#if UNITY_EDITOR



						instance.name = $"(Cached) {instance.name}";

					#endif

				}
				else
				{
					Destroy(instance.gameObject);
				}
			}
			else
			{
				Destroy(instance.gameObject);
			}
		}

		NetworkPrefabId INetworkObjectProvider.GetPrefabId(NetworkRunner runner, NetworkObjectGuid prefabGuid)
		{
			return runner.Prefabs.GetId(prefabGuid);
		}

		// Public method to clear the object pool
		public void ClearObjectPool()
		{

			// Destroy all cached objects
			foreach (var stack in _cached.Values)
			{
				while (stack.Count > 0)
				{
					var obj = stack.Pop();
					if (obj != null)
					{

						Destroy(obj.gameObject);
					}
				}
			}

			// Clear the dictionaries
			_cached.Clear();



		}

		// Public method to force cleanup of all objects including borrowed ones
		// Use this with caution as it will destroy objects that might still be in use
		public void ForceCleanupAllObjects()
		{


			// First clear the cached objects
			ClearObjectPool();

			// Now handle borrowed objects
			List<NetworkObject> objectsToDestroy = new List<NetworkObject>(_borrowed.Keys);

			// Clear the borrowed dictionary first to prevent issues during destruction
			_borrowed.Clear();

			// Now destroy all the borrowed objects
			foreach (var obj in objectsToDestroy)
			{
				if (obj != null)
				{
					obj.gameObject.SetActive(false);
					Destroy(obj.gameObject);
				}
			}

		}

		// Public method to clean up player objects specifically
		// This is useful when ending a session to ensure player objects don't reappear
		public void CleanupPlayerObjects()
		{

			// Clean up cached player objects
			foreach (var kvp in _cached)
			{
				var stack = kvp.Value;
				var tempStack = new Stack<NetworkObject>();

				// Check each object in the stack
				while (stack.Count > 0)
				{
					var obj = stack.Pop();
					if (obj != null)
					{
						// Check if this is a player object
						if (IsPlayerObject(obj))
						{
							Destroy(obj.gameObject);
						}
						else
						{
							// Keep non-player objects
							tempStack.Push(obj);
						}
					}
				}

				// Restore non-player objects to the original stack
				while (tempStack.Count > 0)
				{
					stack.Push(tempStack.Pop());
				}
			}

			// Find and deactivate any borrowed player objects
			List<NetworkObject> playerObjectsToRemove = new List<NetworkObject>();

			foreach (var kvp in _borrowed)
			{
				var obj = kvp.Key;
				if (obj != null && IsPlayerObject(obj))
				{
					obj.gameObject.SetActive(false);
					playerObjectsToRemove.Add(obj);
				}
			}

			// Remove the player objects from the borrowed dictionary
			foreach (var obj in playerObjectsToRemove)
			{
				_borrowed.Remove(obj);
			}

		}

		// Public method to deactivate ALL pooled objects when a session ends
		// This ensures no objects from previous sessions reappear in new sessions
		public void DeactivateAllPooledObjects()
		{

			// First clean up player objects (these are removed from the pool completely)
			CleanupPlayerObjects();

			// Now handle ALL other objects in the cache - ensure they're deactivated
			int deactivatedCachedObjects = 0;
			foreach (var kvp in _cached)
			{
				var stack = kvp.Value;
				var tempStack = new Stack<NetworkObject>();

				// Check each object in the stack
				while (stack.Count > 0)
				{
					var obj = stack.Pop();
					if (obj != null)
					{
						// Always deactivate objects in the cache
						obj.gameObject.SetActive(false);
						deactivatedCachedObjects++;

						// Special handling for weapon objects
						// if (IsWeaponOrSpecialObject(obj))
						// {

						// 	// Ensure all child objects are also deactivated
						// 	for (int i = 0; i < obj.transform.childCount; i++)
						// 	{
						// 		var child = obj.transform.GetChild(i);
						// 		child.gameObject.SetActive(false);
						// 	}
						// }

						// Keep all objects in the pool
						tempStack.Push(obj);
					}
				}

				// Restore objects to the original stack
				while (tempStack.Count > 0)
				{
					stack.Push(tempStack.Pop());
				}
			}

			// Find and deactivate ALL borrowed objects
			int deactivatedBorrowedObjects = 0;
			foreach (var kvp in _borrowed)
			{
				var obj = kvp.Key;
				if (obj != null)
				{
					// Always deactivate borrowed objects, even if they're already inactive
					obj.gameObject.SetActive(false);
					deactivatedBorrowedObjects++;

					// Special handling for weapon objects
					// if (IsWeaponOrSpecialObject(obj))
					// {

					// 	// Ensure all child objects are also deactivated
					// 	for (int i = 0; i < obj.transform.childCount; i++)
					// 	{
					// 		var child = obj.transform.GetChild(i);
					// 		child.gameObject.SetActive(false);
					// 	}
					// }
				}
			}

			// // Also find and deactivate any active objects in the scene that might be weapons
			// // This is a fallback to catch any objects that might have been missed
			// NetworkObject[] sceneObjects = FindObjectsByType<NetworkObject>(FindObjectsSortMode.None);
			// int deactivatedSceneObjects = 0;

			// foreach (var obj in sceneObjects)
			// {
			// 	// Check if this is a weapon that should be deactivated
			// 	if (obj != null && obj.gameObject.activeSelf && IsWeaponOrSpecialObject(obj))
			// 	{
			// 		// Deactivate the object
			// 		obj.gameObject.SetActive(false);
			// 		deactivatedSceneObjects++;

			// 		// Ensure all child objects are also deactivated
			// 		for (int i = 0; i < obj.transform.childCount; i++)
			// 		{
			// 			var child = obj.transform.GetChild(i);
			// 			child.gameObject.SetActive(false);
			// 		}
			// 	}
			// }


		}

		// Public method to clean up all objects during scene transitions
		// This ensures objects don't persist between scenes
		public void CleanupForSceneTransition()
		{
			//Debug.Log("<color=red>NetworkObjectPool: Cleaning up all objects for scene transition</color>");

			// First, handle the cached objects
			int destroyedCachedObjects = 0;
			foreach (var kvp in _cached)
			{
				var stack = kvp.Value;
				var tempStack = new Stack<NetworkObject>();

				// Check each object in the stack
				while (stack.Count > 0)
				{
					var obj = stack.Pop();
					if (obj != null)
					{
						// Check if it's a player object
						if (IsJustPlayerObject(obj))
						{
							// Keep player objects in the pool
							//tempStack.Push(obj);
							//Debug.Log("<color=green>LEAVING PLAYER OBJEFCTS!!</color>");
						}
						else
						{
							// Destroy non-player objects
							Destroy(obj.gameObject);
							destroyedCachedObjects++;
						}
					}
				}

				// Restore player objects to the original stack
				while (tempStack.Count > 0)
				{
					stack.Push(tempStack.Pop());
				}
			}

			// Next, handle the borrowed objects
			int destroyedBorrowedObjects = 0;
			List<NetworkObject> objectsToRemove = new List<NetworkObject>();

			// First identify all non-player objects to remove
			foreach (var kvp in _borrowed)
			{
				var obj = kvp.Key;
				if (obj != null && !IsJustPlayerObject(obj))
				{
					objectsToRemove.Add(obj);
				}
			}

			// Then remove them from the borrowed dictionary and destroy them
			foreach (var obj in objectsToRemove)
			{
				_borrowed.Remove(obj);
				Destroy(obj.gameObject);
				destroyedBorrowedObjects++;
			}

			// Additional cleanup for any objects that might have been missed
			// Find all NetworkObjects in the scene and destroy any that might be from our pool
			NetworkObject[] sceneObjects = FindObjectsByType<NetworkObject>(FindObjectsSortMode.None);
			int destroyedSceneObjects = 0;

			foreach (var obj in sceneObjects)
			{
				// Skip objects that are explicitly marked to persist (like player objects)
				if (obj != null && !IsJustPlayerObject(obj) && !IsPersistentObject(obj))
				{
					// Destroy the object
					Destroy(obj.gameObject);
					destroyedSceneObjects++;
				}
			}

			//Debug.Log($"<color=red>NetworkObjectPool: Destroyed {destroyedCachedObjects} cached, {destroyedBorrowedObjects} borrowed, and {destroyedSceneObjects} scene objects during transition</color>");
		}

		// Helper method to check if an object is marked to persist across scenes
		private bool IsPersistentObject(NetworkObject obj)
		{
			if (obj == null)
				return false;

			// Check if it's a player object (these are usually meant to persist)
			if (IsPlayerObject(obj))
				return true;

			// Check if the object is in the DontDestroyOnLoad scene
			UnityEngine.SceneManagement.Scene objScene = obj.gameObject.scene;
			return objScene.buildIndex == -1; // DontDestroyOnLoad scene has buildIndex of -1
		}

		// Helper method to check if an object is a player object
		private bool IsPlayerObject(NetworkObject obj)
		{
			if (obj == null)
				return false;

			// Check if it has a Player component
			if (obj.GetComponent<Player>() != null)
				return true;

			// Check if it has a PlayerAgent component
			if (obj.GetComponent<PlayerAgent>() != null)
				return true;

			// Check the name as a fallback
			string objName = obj.name.ToLower();
			return objName.Contains("player") || objName.Contains("agent");
		}

		private bool IsJustPlayerObject(NetworkObject obj)
		{
			if (obj == null)
				return false;

			// Check if it has a Player component
			if (obj.GetComponent<Player>() != null)
				return true;

				return false;

			

		}

		// Helper method to check if an object is a weapon or special object that should be handled specially
		private bool IsWeaponOrSpecialObject(NetworkObject obj)
		{
			if (obj == null)
				return false;

			// Check if it has a Weapon component
			if (obj.GetComponent<Weapon>() != null)
				return true;

				return false;



		}


		private void AssignContext(NetworkObject instance)
		{
			for (int i = 0, count = instance.NetworkedBehaviours.Length; i < count; i++)
			{
				if (instance.NetworkedBehaviours[i] is IContextBehaviour cachedBehaviour)
				{
					cachedBehaviour.Context = Context;
				}
			}
		}


    }
}
