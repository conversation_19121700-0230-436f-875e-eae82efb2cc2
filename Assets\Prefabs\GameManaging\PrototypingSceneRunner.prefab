%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2008876836079331816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3330215495443098012}
  - component: {fileID: 6028403315648350420}
  - component: {fileID: 2650392608394831044}
  - component: {fileID: 7495648128398051070}
  - component: {fileID: 606417377047937441}
  - component: {fileID: 997388189318410992}
  - component: {fileID: -7448548071891608418}
  - component: {fileID: 6539915978321717486}
  - component: {fileID: 6388832141534802824}
  m_Layer: 0
  m_Name: PrototypingSceneRunner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3330215495443098012
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6028403315648350420
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e439f97d4bd34da48e55cbdead256552, type: 3}
  m_Name:
  m_EditorClassIdentifier:
  _gameplayPrefab: {fileID: 9101716018452992852, guid: fcb6afe0420fd2c46b433291e065e2f6, type: 3}
  _playerPrefab: {fileID: 1783310441255608348, guid: 99427c51693f9f64bba78263a67cf352, type: 3}
--- !u!114 &2650392608394831044
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1199893898, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name:
  m_EditorClassIdentifier:
--- !u!114 &7495648128398051070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -232609262, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name:
  m_EditorClassIdentifier:
  OnInput:
    m_PersistentCalls:
      m_Calls: []
  OnInputMissing:
    m_PersistentCalls:
      m_Calls: []
  OnConnectedToServer:
    m_PersistentCalls:
      m_Calls: []
  OnDisconnectedFromServer:
    m_PersistentCalls:
      m_Calls: []
  OnConnectRequest:
    m_PersistentCalls:
      m_Calls: []
  OnConnectFailed:
    m_PersistentCalls:
      m_Calls: []
  PlayerJoined:
    m_PersistentCalls:
      m_Calls: []
  PlayerLeft:
    m_PersistentCalls:
      m_Calls: []
  OnSimulationMessage:
    m_PersistentCalls:
      m_Calls: []
  OnShutdown:
    m_PersistentCalls:
      m_Calls: []
  OnSessionListUpdate:
    m_PersistentCalls:
      m_Calls: []
  OnCustomAuthenticationResponse:
    m_PersistentCalls:
      m_Calls: []
  OnHostMigration:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadDone:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadStart:
    m_PersistentCalls:
      m_Calls: []
  OnReliableData:
    m_PersistentCalls:
      m_Calls: []
  OnReliableProgress:
    m_PersistentCalls:
      m_Calls: []
  OnObjectEnterAOI:
    m_PersistentCalls:
      m_Calls: []
  OnObjectExitAOI:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &606417377047937441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b24e6d85a69575f48a235efb7f3ed047, type: 3}
  m_Name:
  m_EditorClassIdentifier:
--- !u!114 &997388189318410992
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b8edbe7108d8a43ab838cf8b8d9453df, type: 3}
  m_Name:
  m_EditorClassIdentifier:
--- !u!114 &-7448548071891608418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -49887739, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name:
  m_EditorClassIdentifier:
  BVHDepth: 0
  BVHNodes: 0
  TotalHitboxes: 0
--- !u!114 &6539915978321717486
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7b5a1ff9dee264bfd829a39e6542dcbd, type: 3}
  m_Name:
  m_EditorClassIdentifier:
  _physicsAuthority: 2
  _physicsTiming: 3
  ClientPhysicsSimulation: 3
  DeltaTimeMultiplier: 1
  SetUnityFixedTimestep: 0
--- !u!114 &6388832141534802824
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2008876836079331816}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ebe674a207456da4bb95222116101fff, type: 3}
  m_Name:
  m_EditorClassIdentifier:
  prefabToSpawn: {fileID: 5927017063952025324, guid: 4bc32a4bd4e39d54cb47a33c2da2f0ce, type: 3}
  spawnPoint: {fileID: 0}
  onlySpawnOnServer: 1
  parentToThis: 0
  logDebugInfo: 1
