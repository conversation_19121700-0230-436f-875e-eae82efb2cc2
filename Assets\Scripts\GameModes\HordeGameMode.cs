using System.Collections.Generic;
using UnityEngine;
using Fusion;
using Projectiles;

/// <summary>
/// Standard horde mode where waves of enemies attack players.
/// Players work together to survive increasingly difficult waves.
/// </summary>
public class HordeGameMode : GameModeBase
{
    [Header("Wave Settings")]
    [SerializeField] private float initialCooldown = 30f;
    [SerializeField] private float cooldownBetweenWaves = 20f;
    [SerializeField] private int baseEnemiesPerWave = 10;
    [SerializeField] private float enemyIncreasePerWave = 5f;
    [SerializeField] private int maxActiveEnemies = 10;

    [Header("Enemy Spawning")]
    [SerializeField] private NetworkPrefabRef[] enemyPrefabs;
    [SerializeField] private float timeBetweenSpawns = 1f;

    [Header("Wave Configuration")]
    [SerializeField] private WaveConfig waveConfig;

    // Runtime variables
    private readonly List<NetworkObject> activeEnemies = new();
    private EnemySpawnPoint[] spawnPoints;
    private EnemySpawnPoint lastUsedSpawnPoint;

    [Networked] private TickTimer WaveCooldownTimer { get; set; }
    [Networked] private TickTimer SpawnTimer { get; set; }
    [Networked] private int EnemiesRemainingToSpawn { get; set; }
    [Networked] private int TotalEnemiesInWave { get; set; }
    [Networked] private int EnemiesKilledThisWave { get; set; }
    [Networked] private NetworkBool IsWaveInProgress { get; set; }

    public override void Spawned()
    {
        base.Spawned();

        if (Runner.IsServer)
        {
            // Find spawn points
            spawnPoints = Runner.SimulationUnityScene.FindObjectsOfTypeInOrder<EnemySpawnPoint>(false);

            // Clean up any existing enemies
            CleanupEnemies();
        }
    }

    public override void StartGame()
    {
        if (!Runner.IsServer) return;

        IsGameActive = true;
        CurrentWave = 1; // Start with wave 1 instead of 0 to avoid negative indices
        IsGameOver = false;
        IsWaveInProgress = false; // No wave in progress yet

        // Initialize wave variables to prevent immediate wave completion
        TotalEnemiesInWave = 1; // Set to non-zero to prevent immediate completion
        EnemiesRemainingToSpawn = 0;
        EnemiesKilledThisWave = 0;

        Debug.Log($"Game starting with CurrentWave = {CurrentWave}");

        // Start initial cooldown
        WaveCooldownTimer = TickTimer.CreateFromSeconds(Runner, initialCooldown);

        // Notify listeners (this will also apply weapons to existing players)
        NotifyGameStarted();

        Debug.Log("Horde Mode game started!");
    }

    public override void EndGame()
    {
        if (!Runner.IsServer) return;

        IsGameActive = false;
        IsGameOver = true;

        // Clean up any remaining enemies
        CleanupEnemies();

        // Notify listeners
        NotifyGameOver();

        Debug.Log("Horde Mode game ended!");
    }

    public override void StartNextWave()
    {
        if (!Runner.IsServer) return;

        // Increment wave counter, ensuring it's at least 1
        CurrentWave = Mathf.Max(1, CurrentWave + 1);
        Debug.Log($"Starting wave {CurrentWave}");

        // Calculate enemies for this wave
        int enemiesThisWave;

        if (waveConfig != null)
        {
            // Use wave config if available, ensuring wave index is not negative
            int waveIndex = Mathf.Max(0, CurrentWave - 1);

            // Check if we have valid enemy prefabs in the wave config
            var waveInfo = waveConfig.GetWaveInfo(waveIndex);
            if (waveInfo != null && (waveInfo.enemies == null || waveInfo.enemies.Count == 0))
            {
                Debug.LogWarning($"Wave {waveIndex} has no enemy prefabs defined! Check your WaveConfig asset.");
            }

            enemiesThisWave = waveConfig.CalculateEnemyCount(waveIndex, difficultyMultiplier);
            Debug.Log($"Calculated {enemiesThisWave} enemies for wave {waveIndex}");

            // Ensure we have at least one enemy
            if (enemiesThisWave <= 0)
            {
                Debug.LogWarning("Wave calculation resulted in 0 or negative enemies, setting to minimum of 1");
                enemiesThisWave = 1;
            }
        }
        else
        {
            // Fallback to simple calculation
            enemiesThisWave = baseEnemiesPerWave + Mathf.FloorToInt(enemyIncreasePerWave * (CurrentWave - 1) * difficultyMultiplier);
            Debug.Log($"Using fallback calculation: {enemiesThisWave} enemies for wave {CurrentWave}");
        }

        TotalEnemiesInWave = enemiesThisWave;
        EnemiesRemainingToSpawn = enemiesThisWave;
        EnemiesKilledThisWave = 0;

        // Initialize spawn timer
        SpawnTimer = TickTimer.CreateFromSeconds(Runner, timeBetweenSpawns);

        // Set wave in progress flag
        IsWaveInProgress = true;

        // Notify listeners
        NotifyWaveStarted(CurrentWave);

        Debug.Log($"Wave {CurrentWave} started with {TotalEnemiesInWave} enemies!");
    }

    public override void HandlePlayerJoined(Player player)
    {
        // Handle player joining mid-game if needed
        Debug.Log($"Player joined Horde Mode: {player.Object.InputAuthority}");

        // Apply starting weapons to the player
        ApplyStartingWeaponsToPlayer(player);
    }

    public override void HandlePlayerLeft(Player player)
    {
        // Handle player leaving mid-game
        Debug.Log($"Player left Horde Mode: {player.Object.InputAuthority}");

        // Check if all players left and handle accordingly
        if (Context.Gameplay != null && Context.Gameplay.PlayerCount == 0)
        {
            Debug.Log("All players left, ending game");
            EndGame();
        }
    }

    public override void FixedUpdateNetwork()
    {
        if (!Runner.IsServer) return;

        if (IsGameActive && !IsGameOver)
        {
            // Check if we're in cooldown between waves
            if (WaveCooldownTimer.IsRunning && WaveCooldownTimer.Expired(Runner))
            {
                StartNextWave();
                WaveCooldownTimer = default; // Clear the timer
            }

            // If we're in an active wave, spawn enemies
            if (EnemiesRemainingToSpawn > 0 && activeEnemies.Count < maxActiveEnemies)
            {
                if (SpawnTimer.Expired(Runner))
                {
                    if (spawnPoints != null && spawnPoints.Length > 0)
                    {
                        SpawnEnemy();
                    }

                    // Reset spawn timer
                    SpawnTimer = TickTimer.CreateFromSeconds(Runner, timeBetweenSpawns);
                }
            }

            // Check if wave is complete - only if a wave is actually in progress
            if (IsWaveInProgress && EnemiesRemainingToSpawn <= 0 && activeEnemies.Count == 0 && EnemiesKilledThisWave >= TotalEnemiesInWave)
            {
                // Wave completed
                IsWaveInProgress = false; // Wave is no longer in progress
                NotifyWaveCompleted(CurrentWave);

                Debug.Log($"Wave {CurrentWave} completed! Killed {EnemiesKilledThisWave} of {TotalEnemiesInWave} enemies");

                // Check if game is over
                if (!isEndless && CurrentWave >= maxWaves)
                {
                    Debug.Log("Max waves reached, ending game");
                    EndGame();
                }
                else
                {
                    // Start cooldown for next wave
                    float nextWaveCooldown = cooldownBetweenWaves;

                    // Use wave config if available
                    if (waveConfig != null)
                    {
                        // Use the current wave index, ensuring it's not negative
                        int waveIndex = Mathf.Max(0, CurrentWave - 1);
                        var waveInfo = waveConfig.GetWaveInfo(waveIndex);
                        if (waveInfo != null)
                        {
                            nextWaveCooldown = waveInfo.cooldownAfterWave;
                            Debug.Log($"Using cooldown {nextWaveCooldown} from wave info for wave {waveIndex}");
                        }
                    }

                    WaveCooldownTimer = TickTimer.CreateFromSeconds(Runner, nextWaveCooldown);

                    Debug.Log($"Next wave in {nextWaveCooldown} seconds");
                }
            }
        }
    }

    private void SpawnEnemy()
    {
        // Check if we can spawn enemies
        if (spawnPoints == null || spawnPoints.Length == 0)
        {
            Debug.LogError("Cannot spawn enemy: No spawn points available!");
            return;
        }

        if (EnemiesRemainingToSpawn <= 0)
        {
            Debug.Log("No more enemies remaining to spawn for this wave");
            return;
        }

        // Select a random spawn point (avoiding the last used one if possible)
        EnemySpawnPoint spawnPoint;
        do
        {
            spawnPoint = spawnPoints[Random.Range(0, spawnPoints.Length)];
        }
        while (spawnPoint == lastUsedSpawnPoint && spawnPoints.Length > 1);

        lastUsedSpawnPoint = spawnPoint;

        // Select an enemy prefab
        NetworkPrefabRef enemyPrefab;

        if (waveConfig != null)
        {
            // Use wave config to select enemy, ensuring wave index is not negative
            int waveIndex = Mathf.Max(0, CurrentWave - 1);
            enemyPrefab = waveConfig.SelectEnemyPrefab(waveIndex, difficultyMultiplier);
        }
        else
        {
            // Fallback to random selection from array
            enemyPrefab = enemyPrefabs[Random.Range(0, enemyPrefabs.Length)];
        }

        // Ensure we have a valid prefab
        if (enemyPrefab == default)
        {
            Debug.LogError("No valid enemy prefab selected! Check your WaveConfig asset or enemyPrefabs array.");

            // If we have fallback prefabs defined, use one of those
            if (enemyPrefabs != null && enemyPrefabs.Length > 0)
            {
                Debug.Log("Using fallback enemy prefab from enemyPrefabs array");
                enemyPrefab = enemyPrefabs[0];
            }
            else
            {
                Debug.LogError("No fallback enemy prefabs available! Cannot spawn enemy.");
                return;
            }
        }

        // Spawn the enemy
        NetworkObject enemyObj = Runner.Spawn(enemyPrefab, spawnPoint.transform.position, Quaternion.identity);

        // Set up the enemy
        if (enemyObj.TryGetComponent(out EnemyAIBase enemy))
        {
            enemy.OnDeath += HandleEnemyDeath;
            activeEnemies.Add(enemyObj);
            EnemiesRemainingToSpawn--;

            Debug.Log($"Spawned enemy at {spawnPoint.name}, {EnemiesRemainingToSpawn} remaining to spawn");
        }
        else
        {
            Debug.LogError("Spawned object does not have EnemyAIBase component!");
            Runner.Despawn(enemyObj);
        }
    }

    private void HandleEnemyDeath(NetworkObject enemyObj)
    {
        activeEnemies.Remove(enemyObj);
        EnemiesKilledThisWave++;

        Debug.Log($"Enemy killed, {activeEnemies.Count} active enemies remaining, {EnemiesKilledThisWave}/{TotalEnemiesInWave} killed this wave");
    }

    private void CleanupEnemies()
    {
        // Clean up any existing enemies
        foreach (var enemy in activeEnemies)
        {
            if (enemy && enemy.IsValid)
            {
                Runner.Despawn(enemy);
            }
        }
        activeEnemies.Clear();
    }

    // Apply configuration from a WaveConfig
    public void ApplyWaveConfig(WaveConfig config)
    {
        if (!HasStateAuthority || config == null)
        {
            Debug.LogWarning("Cannot apply wave config: No state authority or config is null");
            return;
        }

        waveConfig = config;
        isEndless = config.isEndless;
        difficultyMultiplier = config.globalDifficultyMultiplier;

        // Log detailed information about the wave config
        Debug.Log($"Applied wave config: Endless={isEndless}, Difficulty={difficultyMultiplier}");

        // Log information about the waves in the config
        if (config.waves != null)
        {
            Debug.Log($"Wave config contains {config.waves.Count} waves");

            // Log details of the first few waves
            for (int i = 0; i < Mathf.Min(3, config.waves.Count); i++)
            {
                var wave = config.waves[i];
                Debug.Log($"Wave {i}: {wave.waveName}, {wave.baseEnemyCount} enemies, {wave.enemies?.Count ?? 0} enemy types");
            }
        }
        else
        {
            Debug.LogWarning("Wave config has no waves defined!");
        }
    }
}
