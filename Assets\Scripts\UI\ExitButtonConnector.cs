using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// This script connects the Exit Game Button in the settings panel to the exit functionality in InGameUIHolder.
/// Attach this script to the Exit Game Button in the In Game Settings Menu prefab.
/// </summary>
public class ExitButtonConnector : MonoBehaviour
{
    [SerializeField]
    private Button exitButton;
    [SerializeField]
    private InGameUIHolder inGameUIHolder;
    private OVRCameraRig headsetRig;
    private NetworkManager networkManager;

    private void Awake()
    {




        // Find the OVRCameraRig
        headsetRig =inGameUIHolder.hardwareRigSettings.hardwareRig.headset;
        Debug.Assert(headsetRig, "headsetRig not found in ExitButtonConnector " + gameObject.name);

        // Get NetworkManager reference
        networkManager = NetworkManager.Instance;

        if (exitButton != null && inGameUIHolder != null && headsetRig != null)
        {
            // Connect the button click to the exit functionality
            exitButton.onClick.AddListener(() => inGameUIHolder.OnExitButtonClicked());
        }
        else
        {
            Debug.LogError("ExitButtonConnector: Missing required components. Make sure the button is a child of InGameUIHolder and OVRCameraRig exists in the scene.");
        }
    }

    private void OnEnable()
    {
        // Update button interactability whenever it becomes visible
        UpdateButtonInteractability();
    }

    private void Update()
    {
        // Periodically update button interactability
        // This ensures the button is disabled when in the hub world
        UpdateButtonInteractability();
    }

    private void UpdateButtonInteractability()
    {
        if (exitButton != null && networkManager != null)
        {
            // Only enable the exit button if we're in a game scene (not hub world)
            exitButton.interactable = networkManager.IsInGameScene();
        }
    }
}
