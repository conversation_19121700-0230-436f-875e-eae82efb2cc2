using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Projectiles;
/// <summary>
/// UI for selecting and configuring game modes in the lobby.
/// </summary>
public class GameModeSelectionUI : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private TMP_Dropdown gameModeDropdown;
    [SerializeField] private Toggle endlessToggle;
    [SerializeField] private TMP_InputField maxWavesInput;
    [SerializeField] private Slider difficultySlider;
    [SerializeField] private TextMeshProUGUI difficultyText;
    [SerializeField] private Toggle teamModeToggle;
    [SerializeField] private TMP_Dropdown teamCountDropdown;

    [Header("Game Mode References")]
    [SerializeField] private GameModeManager gameModeManager;

    // Game mode configuration
    private List<string> gameModeNames = new List<string>();
    private int selectedGameModeIndex = 0;
    private bool isEndless = false;
    private int maxWaves = 10;
    private float difficultyMultiplier = 1.0f;
    private bool isTeamMode = false;
    private int teamCount = 2;

    private void Start()
    {
        // Initialize UI
        InitializeUI();

        // Add listeners
        AddListeners();

        // Subscribe to game mode manager events
        if (gameModeManager != null)
        {
            gameModeManager.OnGameModeIndexChanged += OnGameModeIndexChanged;
        }
    }

    private void OnDestroy()
    {
        // Unsubscribe from game mode manager events
        if (gameModeManager != null)
        {
            gameModeManager.OnGameModeIndexChanged -= OnGameModeIndexChanged;
        }
    }

    private void InitializeUI()
    {
        // Populate game mode dropdown
        gameModeDropdown.ClearOptions();
        gameModeNames.Clear();

        // Add game mode names from GameModeManager
        if (gameModeManager != null)
        {
            gameModeNames = gameModeManager.GetAvailableGameModeNames();
        }
        else
        {
            // Fallback options
            gameModeNames.Add("Horde Mode");
            gameModeNames.Add("Team Mode");
        }

        gameModeDropdown.AddOptions(gameModeNames);

        // Set default values
        endlessToggle.isOn = isEndless;
        maxWavesInput.text = maxWaves.ToString();
        difficultySlider.value = difficultyMultiplier;
        difficultyText.text = $"Difficulty: {difficultyMultiplier:F1}x";
        teamModeToggle.isOn = isTeamMode;

        // Populate team count dropdown
        teamCountDropdown.ClearOptions();
        List<string> teamCounts = new List<string>();
        for (int i = 2; i <= 4; i++)
        {
            teamCounts.Add($"{i} Teams");
        }
        teamCountDropdown.AddOptions(teamCounts);
        teamCountDropdown.value = teamCount - 2; // Adjust for 0-based index

        // Update UI state
        UpdateUIState();
    }

    private void AddListeners()
    {
        // Add listeners to UI elements
        gameModeDropdown.onValueChanged.AddListener(OnGameModeChanged);
        endlessToggle.onValueChanged.AddListener(OnEndlessToggled);
        maxWavesInput.onEndEdit.AddListener(OnMaxWavesChanged);
        difficultySlider.onValueChanged.AddListener(OnDifficultyChanged);
        teamModeToggle.onValueChanged.AddListener(OnTeamModeToggled);
        teamCountDropdown.onValueChanged.AddListener(OnTeamCountChanged);
    }

    private void UpdateUIState()
    {
        // Enable/disable UI elements based on current settings
        maxWavesInput.interactable = !isEndless;

        // Show/hide team-specific options based on selected game mode
        bool isTeamGameMode = selectedGameModeIndex == 1; // Assuming index 1 is Team Mode
        teamModeToggle.gameObject.SetActive(isTeamGameMode);
        teamCountDropdown.gameObject.SetActive(isTeamGameMode && isTeamMode);

        // Only enable UI elements if we're the host
        bool isHost = NetworkManager.Instance != null && NetworkManager.Instance.IsHost;
        gameModeDropdown.interactable = isHost;
        endlessToggle.interactable = isHost;
        maxWavesInput.interactable = isHost && !isEndless;
        difficultySlider.interactable = isHost;
        teamModeToggle.interactable = isHost && isTeamGameMode;
        teamCountDropdown.interactable = isHost && isTeamGameMode && isTeamMode;
    }

    // Event handlers
    private void OnGameModeChanged(int index)
    {
        selectedGameModeIndex = index;
        UpdateUIState();

        // Update game mode in GameModeManager if we're the host
        if (NetworkManager.Instance != null && NetworkManager.Instance.IsHost)
        {
            gameModeManager?.SetGameMode(selectedGameModeIndex);
        }
    }

    private void OnEndlessToggled(bool value)
    {
        isEndless = value;
        UpdateUIState();
        UpdateGameConfig();
    }

    private void OnMaxWavesChanged(string value)
    {
        if (int.TryParse(value, out int waves))
        {
            maxWaves = Mathf.Clamp(waves, 1, 100);
            maxWavesInput.text = maxWaves.ToString();
            UpdateGameConfig();
        }
    }

    private void OnDifficultyChanged(float value)
    {
        difficultyMultiplier = value;
        difficultyText.text = $"Difficulty: {difficultyMultiplier:F1}x";
        UpdateGameConfig();
    }

    private void OnTeamModeToggled(bool value)
    {
        isTeamMode = value;
        UpdateUIState();
        UpdateGameConfig();
    }

    private void OnTeamCountChanged(int index)
    {
        teamCount = index + 2; // Adjust for 0-based index
        UpdateGameConfig();
    }

    private void UpdateGameConfig()
    {
        // Only the host can update the game configuration
        if (NetworkManager.Instance == null || !NetworkManager.Instance.IsHost)
            return;

        // Create a new WaveConfig based on current settings
        WaveConfig config = ScriptableObject.CreateInstance<WaveConfig>();
        config.isEndless = isEndless;
        config.globalDifficultyMultiplier = difficultyMultiplier;

        // Add some default wave settings
        if (config.waves.Count == 0)
        {
            // Add some default waves
            for (int i = 0; i < 10; i++)
            {
                WaveConfig.WaveInfo wave = new WaveConfig.WaveInfo
                {
                    waveName = $"Wave {i + 1}",
                    baseEnemyCount = 5 + (i * 2),
                    timeBetweenSpawns = Mathf.Max(0.5f, 1.0f - (i * 0.05f)),
                    cooldownAfterWave = Mathf.Max(10f, 20f - (i * 1f)),
                    difficultyIncrease = 0.1f
                };

                config.waves.Add(wave);
            }
        }

        // Update the game mode manager with the new config
        gameModeManager?.UpdateGameConfig(config);
    }

    // Called when the game mode index changes in the GameModeManager
    private void OnGameModeIndexChanged(int index)
    {
        // Update the dropdown to match the server's selection
        if (gameModeDropdown.value != index)
        {
            gameModeDropdown.value = index;
        }
    }
}