using UnityEngine;
using TMPro;

public class RoomCodeDisplay : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI _roomCodeText;
    [SerializeField] private GameObject _roomCodePanel;
    
    private NetworkManager _networkManager;

    private void Start()
    {
        // Get reference to NetworkManager
        _networkManager = NetworkManager.Instance;
        if (_networkManager == null)
        {
            Debug.LogError("NetworkManager instance not found!");
            return;
        }

        // Hide the panel initially
        if (_roomCodePanel != null)
        {
            _roomCodePanel.SetActive(false);
        }

        // Update the room code display
        UpdateRoomCodeDisplay();
    }

    private void Update()
    {
        // Only show room code if we're hosting
        if (_networkManager != null && _networkManager.IsConnected)
        {
            if (_networkManager.IsHost && !_roomCodePanel.activeSelf)
            {
                _roomCodePanel.SetActive(true);
                UpdateRoomCodeDisplay();
            }
            else if (!_networkManager.IsHost && _roomCodePanel.activeSelf)
            {
                _roomCodePanel.SetActive(false);
            }
        }
        else if (_roomCodePanel.activeSelf)
        {
            _roomCodePanel.SetActive(false);
        }
    }

    private void UpdateRoomCodeDisplay()
    {
        if (_roomCodeText != null && _networkManager != null)
        {
            string roomCode = _networkManager.GetCurrentRoomCode();
            if (!string.IsNullOrEmpty(roomCode))
            {
                _roomCodeText.text = $"Room Code: {roomCode}";
            }
            else
            {
                _roomCodeText.text = "No Room Code";
            }
        }
    }

    public void CopyRoomCodeToClipboard()
    {
        if (_networkManager != null)
        {
            string roomCode = _networkManager.GetCurrentRoomCode();
            if (!string.IsNullOrEmpty(roomCode))
            {
                GUIUtility.systemCopyBuffer = roomCode;
                Debug.Log($"Room code copied to clipboard: {roomCode}");
            }
        }
    }
}
