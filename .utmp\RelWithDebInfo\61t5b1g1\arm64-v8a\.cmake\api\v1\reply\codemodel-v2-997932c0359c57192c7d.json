{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}, {"build": "GameActivity", "jsonFile": "directory-GameActivity-RelWithDebInfo-3edf2ef9e25ca0503118.json", "minimumCMakeVersion": {"string": "3.4.1"}, "parentIndex": 0, "projectIndex": 1, "source": "GameActivity", "targetIndexes": [0]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "Unity"}, {"directoryIndexes": [1], "name": "game", "parentIndex": 0, "targetIndexes": [0]}], "targets": [{"directoryIndex": 1, "id": "game::@d02bb112ea9f9c2ed29f", "jsonFile": "target-game-RelWithDebInfo-9becb8c41e134695b927.json", "name": "game", "projectIndex": 1}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a", "source": "C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/Library/Bee/Android/Prj/IL2CPP/Gradle/unityLibrary/src/main/cpp"}, "version": {"major": 2, "minor": 3}}