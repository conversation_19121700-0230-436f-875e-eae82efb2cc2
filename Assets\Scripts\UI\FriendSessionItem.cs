using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class FriendSessionItem : MonoBehaviour
{
    [SerializeField] private TextMeshProUGUI _sessionNameText;
    [SerializeField] private TextMeshProUGUI _playerCountText;
    [SerializeField] private Button _joinButton;

    private string _sessionName;

    public void Initialize(string sessionName, int playerCount, int maxPlayers)
    {
        _sessionName = sessionName;
        _sessionNameText.text = sessionName;
        _playerCountText.text = $"{playerCount}/{maxPlayers}";
    }

    public void SetJoinCallback(System.Action<string> joinCallback)
    {
        _joinButton.onClick.RemoveAllListeners();
        _joinButton.onClick.AddListener(() => joinCallback?.Invoke(_sessionName));
    }
}
