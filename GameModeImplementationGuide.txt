# Game Mode System Implementation Guide

This guide explains how to implement and use the modular game mode system for your VR multiplayer FPS game.

## System Overview

The game mode system is designed to be modular, efficient, and customizable, allowing you to:
- Create different types of wave-based game modes (horde, team-based, etc.)
- Configure wave parameters (enemy count, types, difficulty)
- Set game mode conditions (endless, limited waves, time-based)
- Support team-based gameplay

## Core Components

### 1. GameModeBase (Abstract Base Class)
- Provides common functionality for all game modes
- Handles game state (active, over, current wave)
- Defines events for game and wave state changes
- Abstract methods that derived classes must implement:
  - StartGame()
  - EndGame()
  - StartNextWave()
  - HandlePlayerJoined(Player)
  - HandlePlayerLeft(Player)

### 2. HordeGameMode
- Standard wave-based mode where players fight against increasingly difficult waves
- Spawns enemies based on wave configuration
- Tracks wave progress and enemy counts
- Supports endless mode or limited waves

### 3. TeamBasedGameMode
- Divides players into teams
- Tracks team scores
- Applies team colors to player agents
- Supports team-based competition while fighting waves of enemies

### 4. WaveConfig (ScriptableObject)
- Configures wave parameters (enemy count, spawn rates, difficulty)
- Supports different enemy types with weighted spawning
- Allows for difficulty scaling as waves progress
- Can be created and customized in the editor

### 5. GameModeManager
- Manages game mode selection and initialization
- Stores available game modes and their configurations
- Handles game mode switching
- Coordinates with NetworkManager for scene transitions

### 6. Supporting Components
- TeamColorApplier: Applies team colors to player agents
- GameModeSelectionUI: UI for selecting and configuring game modes
- DefaultWaveConfig: Creates default wave configuration assets
- GameModeSetup: Sets up game modes in game scenes

## Implementation Steps

### Step 1: Set Up Game Mode Prefabs
1. Create prefabs for each game mode (HordeGameMode, TeamBasedGameMode)
2. Create a prefab for the GameModeManager
3. Add the prefabs to your Resources folder or a dedicated prefabs folder

### Step 2: Create Wave Configurations
1. Use the DefaultWaveConfig script to create wave configuration assets
2. Customize the wave configurations for different difficulty levels
3. Assign the wave configurations to the game mode prefabs

### Step 3: Set Up Game Scenes
1. Add a GameObject with the GameModeSetup component to each game scene
2. Assign the GameModeManager prefab to the GameModeSetup component
3. Assign the game mode prefabs to the GameModeSetup component

### Step 4: Set Up UI
1. Add the GameModeSelectionUI to your lobby panel
2. Configure the UI to show appropriate options for each game mode
3. Connect the UI to the GameModeManager

### Step 5: Connect to NetworkManager
1. Update the NetworkManager's StartLobbyGame method to load the selected game scene
2. The GameModeManager will be initialized in the new scene
3. The GameModeManager will start the selected game mode

## Usage Examples

### Creating a New Game Mode
```csharp
// Create a new class that inherits from GameModeBase
public class CustomGameMode : GameModeBase
{
    // Implement the required abstract methods
    public override void StartGame()
    {
        // Initialize game state
        IsGameActive = true;
        CurrentWave = 0;
        IsGameOver = false;
        
        // Start the first wave or cooldown
        // ...
        
        // Notify listeners
        NotifyGameStarted();
    }
    
    public override void EndGame()
    {
        // Clean up game state
        IsGameActive = false;
        IsGameOver = true;
        
        // Clean up any remaining enemies
        // ...
        
        // Notify listeners
        NotifyGameOver();
    }
    
    // Implement other required methods
    // ...
}
```

### Configuring a Game Mode
```csharp
// Get the game mode manager
var gameModeManager = FindObjectOfType<GameModeManager>();

// Set the game mode
gameModeManager.SetGameMode(0); // 0 = Horde Mode, 1 = Team Mode, etc.

// Update game configuration
WaveConfig config = ScriptableObject.CreateInstance<WaveConfig>();
config.isEndless = true;
config.globalDifficultyMultiplier = 1.5f;
// Add waves to the config
// ...

gameModeManager.UpdateGameConfig(config);
```

### Starting a Game
```csharp
// Get the game mode manager
var gameModeManager = FindObjectOfType<GameModeManager>();

// Start the game
gameModeManager.StartGame();
```

### Handling Game Events
```csharp
// Get the active game mode
var gameMode = gameModeManager.GetActiveGameMode();

// Subscribe to events
gameMode.OnGameStarted += OnGameStarted;
gameMode.OnGameOver += OnGameOver;
gameMode.OnWaveStarted += OnWaveStarted;
gameMode.OnWaveCompleted += OnWaveCompleted;

// Event handlers
private void OnGameStarted()
{
    Debug.Log("Game started!");
    // Update UI, play sound, etc.
}

private void OnGameOver()
{
    Debug.Log("Game over!");
    // Show results, play sound, etc.
}

private void OnWaveStarted(int waveNumber)
{
    Debug.Log($"Wave {waveNumber} started!");
    // Update UI, play sound, etc.
}

private void OnWaveCompleted(int waveNumber)
{
    Debug.Log($"Wave {waveNumber} completed!");
    // Give rewards, update UI, play sound, etc.
}
```

## Troubleshooting

### Common Issues
1. **Game mode not starting**: Make sure the GameModeManager is properly initialized and the StartGame method is called.
2. **Enemies not spawning**: Check that the wave configuration is properly set up and the enemy prefabs are assigned.
3. **Network synchronization issues**: Ensure all networked properties are properly marked with [Networked] and synchronized across clients.

### Debugging Tips
1. Enable debug logs in the GameModeBase and derived classes to track game state changes.
2. Use the Unity Profiler to monitor performance, especially during wave transitions and enemy spawning.
3. Test with multiple clients to ensure network synchronization is working correctly.

## Extending the System

### Adding New Enemy Types
1. Create new enemy prefabs with the EnemyAIBase component
2. Add the enemy prefabs to the wave configuration
3. Adjust spawn weights and difficulty scaling as needed

### Creating New Game Modes
1. Create a new class that inherits from GameModeBase
2. Implement the required abstract methods
3. Add custom functionality for your game mode
4. Create a prefab for your new game mode
5. Add the prefab to the GameModeManager's available game modes

### Adding New Wave Types
1. Extend the WaveConfig class to support new wave types
2. Update the game modes to handle the new wave types
3. Create new wave configuration assets for the new wave types

## Best Practices

1. **Keep game modes modular**: Each game mode should be self-contained and not depend on other game modes.
2. **Use events for communication**: Use events to notify other systems about game state changes.
3. **Centralize configuration**: Use ScriptableObjects for configuration to make it easy to tweak values.
4. **Test thoroughly**: Test with multiple clients to ensure network synchronization is working correctly.
5. **Document your code**: Add comments and documentation to make it easier for others to understand and extend your code.

## Conclusion

This modular game mode system provides a solid foundation for your VR multiplayer FPS game. It's designed to be flexible and extensible, allowing you to easily add new game modes and features as your game evolves.

By following this guide, you should be able to implement and use the game mode system in your game. If you encounter any issues or have questions, refer to the code documentation or reach out for assistance.
