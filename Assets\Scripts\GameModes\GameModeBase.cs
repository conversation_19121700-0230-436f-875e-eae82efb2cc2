using System;
using System.Collections.Generic;
using UnityEngine;
using Fusion;
using Projectiles;

/// <summary>
/// Base class for all game modes. Provides common functionality and interfaces for different game types.
/// </summary>
public abstract class GameModeBase : ContextBehaviour
{
    [Networked] public NetworkBool IsGameActive { get; set; }
    [Networked] public int CurrentWave { get; set; }
    [Networked] public NetworkBool IsGameOver { get; set; }

    // Events
    public event Action<int> OnWaveStarted;
    public event Action<int> OnWaveCompleted;
    public event Action OnGameStarted;
    public event Action OnGameOver;

    // Configuration
    [Header("Game Mode Settings")]
    [SerializeField] protected bool isEndless = false;
    [SerializeField] protected int maxWaves = 10;
    [SerializeField] protected float difficultyMultiplier = 1.0f;

    [Header("Player Weapon Settings")]
    [Tooltip("The weapon slot to equip in the player's right hand (0 = empty hand)")]
    [SerializeField] protected int startingRightWeaponSlot = 0;
    [Tooltip("The weapon slot to equip in the player's left hand (0 = empty hand)")]
    [SerializeField] protected int startingLeftWeaponSlot = 0;

    // Abstract methods that derived classes must implement
    public abstract void StartGame();
    public abstract void EndGame();
    public abstract void StartNextWave();
    public abstract void HandlePlayerJoined(Player player);
    public abstract void HandlePlayerLeft(Player player);

    // Common functionality
    protected virtual void InitializeGameMode()
    {
        CurrentWave = 0;
        IsGameActive = false;
        IsGameOver = false;
    }

    public override void Spawned()
    {
        base.Spawned();
        InitializeGameMode();
    }

    // Helper methods for derived classes
    protected void NotifyWaveStarted(int waveNumber)
    {
        OnWaveStarted?.Invoke(waveNumber);
    }

    protected void NotifyWaveCompleted(int waveNumber)
    {
        OnWaveCompleted?.Invoke(waveNumber);
    }

    protected void NotifyGameStarted()
    {
        OnGameStarted?.Invoke();

        // Apply starting weapons to all existing players
        if (HasStateAuthority && Context != null && Context.Gameplay != null)
        {
            Debug.Log("Applying starting weapons to all existing players");
            foreach (var playerEntry in Context.Gameplay.Players)
            {
                ApplyStartingWeaponsToPlayer(playerEntry.Value);
            }
        }
    }

    protected void NotifyGameOver()
    {
        OnGameOver?.Invoke();
    }

    // Configuration methods
    public virtual void SetEndless(bool endless)
    {
        if (HasStateAuthority)
        {
            isEndless = endless;
        }
    }

    public virtual void SetMaxWaves(int waves)
    {
        if (HasStateAuthority)
        {
            maxWaves = Mathf.Max(1, waves);
        }
    }

    public virtual void SetDifficultyMultiplier(float multiplier)
    {
        if (HasStateAuthority)
        {
            difficultyMultiplier = Mathf.Max(0.1f, multiplier);
        }
    }

    // Set starting weapon slots
    public virtual void SetStartingWeaponSlots(int rightWeaponSlot, int leftWeaponSlot)
    {
        if (HasStateAuthority)
        {
            startingRightWeaponSlot = Mathf.Max(0, rightWeaponSlot);
            startingLeftWeaponSlot = Mathf.Max(0, leftWeaponSlot);
            Debug.Log($"Set starting weapon slots: Right={startingRightWeaponSlot}, Left={startingLeftWeaponSlot}");
        }
    }

    // Apply starting weapons to a player
    protected virtual void ApplyStartingWeaponsToPlayer(Player player)
    {
        if (!HasStateAuthority || player == null) return;

        // Start a coroutine to apply weapons with a delay to ensure the player agent is fully initialized
        StartCoroutine(ApplyStartingWeaponsWithDelay(player));
    }

    // Coroutine to apply weapons with a delay
    private System.Collections.IEnumerator ApplyStartingWeaponsWithDelay(Player player)
    {
        // Wait a short time to ensure the player agent is fully initialized
        yield return new WaitForSeconds(1.0f);

        if (!HasStateAuthority || player == null) yield break;

        Debug.Log($"Attempting to apply weapons to player {player.Object.InputAuthority} after delay");

        // Get the player's agent
        var playerAgent = player.ActiveAgent;
        if (playerAgent == null)
        {
            Debug.LogWarning("Cannot apply starting weapons: Player agent is null after delay");
            yield break;
        }

        // Get the weapons component
        var weapons = playerAgent.Weapons;
        if (weapons == null)
        {
            Debug.LogWarning("Cannot apply starting weapons: Weapons component is null");
            yield break;
        }

        // Log available weapons to help diagnose issues
        weapons.LogAvailableWeapons();

        // Set the weapons
        Debug.Log($"Setting player {player.Object.InputAuthority} weapons: Right={startingRightWeaponSlot}, Left={startingLeftWeaponSlot}");

        // Try to apply weapons multiple times to ensure they're set
        for (int attempt = 0; attempt < 3; attempt++)
        {
            // Switch to the specified weapon slots
            if (startingRightWeaponSlot > 0)
            {
                weapons.SwitchWeaponRightHand(startingRightWeaponSlot, true);
                Debug.Log($"Applied right weapon slot {startingRightWeaponSlot} to player {player.Object.InputAuthority}, attempt {attempt+1}");
            }

            if (startingLeftWeaponSlot > 0)
            {
                weapons.SwitchWeaponLeftHand(startingLeftWeaponSlot, true);
                Debug.Log($"Applied left weapon slot {startingLeftWeaponSlot} to player {player.Object.InputAuthority}, attempt {attempt+1}");
            }

            // Wait a short time between attempts
            yield return new WaitForSeconds(0.5f);
        }

        // Verify the weapons were applied correctly
        Debug.Log($"Final weapon slots for player {player.Object.InputAuthority}: Right={weapons.CurrentRightWeaponSlot}, Left={weapons.CurrentLeftWeaponSlot}");
    }

    // Getters for configuration
    public bool IsEndless => isEndless;
    public int MaxWaves => maxWaves;
    public float DifficultyMultiplier => difficultyMultiplier;
    public int StartingRightWeaponSlot => startingRightWeaponSlot;
    public int StartingLeftWeaponSlot => startingLeftWeaponSlot;
}
