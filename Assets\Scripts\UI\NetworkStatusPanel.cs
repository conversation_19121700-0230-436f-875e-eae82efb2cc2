using System.Collections;
using UnityEngine;
using TMPro;

public class NetworkStatusPanel : MonoBehaviour
{
    [Header("UI References")]
    [SerializeField] private TextMeshProUGUI _titleText;
    [SerializeField] private TextMeshProUGUI _messageText;
    [SerializeField] private TextMesh<PERSON><PERSON>UGUI _roomCodeText;

    private Coroutine _autoHideCoroutine;

    private void Awake()
    {
        // Make sure the panel is hidden at start
        gameObject.SetActive(false);
    }

    /// <summary>
    /// Shows the network status panel with the specified title and message
    /// </summary>
    /// <param name="title">Title to display</param>
    /// <param name="message">Message to display</param>
    /// <param name="roomCode">Optional room code to display</param>
    /// <param name="autoHideDelay">If greater than 0, panel will auto-hide after this many seconds</param>
    public void ShowStatus(string title, string message, string roomCode = "", float autoHideDelay = 0f)
    {
        // Cancel any existing auto-hide coroutine
        if (_autoHideCoroutine != null)
        {
            StopCoroutine(_autoHideCoroutine);
            _autoHideCoroutine = null;
        }

        // Set the text values
        _titleText.text = title;
        _messageText.text = message;

        // Show or hide the room code based on whether it's provided
        if (string.IsNullOrEmpty(roomCode))
        {
            _roomCodeText.gameObject.SetActive(false);
        }
        else
        {
            _roomCodeText.gameObject.SetActive(true);
            _roomCodeText.text = $"Room Code: {roomCode}";
        }

        // Show the panel
        gameObject.SetActive(true);

        // Start auto-hide coroutine if delay is specified
        if (autoHideDelay > 0f)
        {
            _autoHideCoroutine = StartCoroutine(AutoHideAfterDelay(autoHideDelay));
        }
    }

    /// <summary>
    /// Hides the network status panel
    /// </summary>
    public void HideStatus()
    {
        // Cancel any existing auto-hide coroutine
        if (_autoHideCoroutine != null)
        {
            StopCoroutine(_autoHideCoroutine);
            _autoHideCoroutine = null;
        }
        // Hide the panel
        if(gameObject)      
        gameObject.SetActive(false);
    }

    /// <summary>
    /// Updates the message text without changing other settings
    /// </summary>
    /// <param name="message">New message to display</param>
    public void UpdateMessage(string message)
    {
        _messageText.text = message;
    }

    /// <summary>
    /// Updates the room code text without changing other settings
    /// </summary>
    /// <param name="roomCode">New room code to display</param>
    public void UpdateRoomCode(string roomCode)
    {
        if (string.IsNullOrEmpty(roomCode))
        {
            _roomCodeText.gameObject.SetActive(false);
        }
        else
        {
            _roomCodeText.gameObject.SetActive(true);
            _roomCodeText.text = $"Room Code: {roomCode}";
        }
    }

    /// <summary>
    /// Coroutine to auto-hide the panel after a delay
    /// </summary>
    private IEnumerator AutoHideAfterDelay(float delay)
    {
        yield return new WaitForSeconds(delay);
        HideStatus();
        _autoHideCoroutine = null;
    }
}
