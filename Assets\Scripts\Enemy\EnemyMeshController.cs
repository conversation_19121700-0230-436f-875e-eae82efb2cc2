using Fusion;
using Projectiles;
using UnityEngine;

public class EnemyMeshController : NetworkBehaviour
{
    [SerializeField]
    private Animator animator;
    [SerializeField]
    private SkinnedMesh<PERSON><PERSON><PERSON> meshRenderer;

    [Header("Bones")]
    [SerializeField]
    private Transform headBone;
    [SerializeField]
    private Transform rightArmBone;
    [SerializeField]
    private Transform leftArmBone;
    [SerializeField]
    private Transform rightLegBone;
    [SerializeField]
    private Transform leftLegBone;
    [SerializeField]
    private Transform bodyBone;

    [Header("Hit Boxes")]
    [SerializeField]
    private Hitbox [] headHitBoxes;
    [SerializeField]
    private Hitbox [] rightArmHitBox;
    [SerializeField]
    private Hitbox [] leftArmHitBox;
    [SerializeField]
    private Hitbox [] rightLegHitBox;
    [SerializeField]
    private Hitbox []leftLegHitBox;
    [SerializeField]
    private Hitbox [] bodyHitBox;

    [Header("VFX")]
    [SerializeField]
    private GameObject headVFX;
    [SerializeField]
    private GameObject rightArmVFX;
    [SerializeField]
    private GameObject leftArmVFX;
    [SerializeField]
    private GameObject rightLegVFX;
    [SerializeField]
    private GameObject leftLegVFX;
    [SerializeField]
    private GameObject bodyExplosionVFX;

    private GameObject activeVFX;


    [Networked]
    private int walkAnimationValue { get; set; }

    private static readonly int walkAnim = Animator.StringToHash("WalkAnim");
    private static readonly int rightArmHitAnim = Animator.StringToHash("RightArmHit");
    private static readonly int leftArmHitAnim = Animator.StringToHash("LeftArmHit");
    private static readonly int legHitAnim = Animator.StringToHash("LegHit");
    private static readonly int isCrawlStateAnim = Animator.StringToHash("isCrawlState");
    private static readonly int isDeadAnim = Animator.StringToHash("isDead");
    private static readonly int bodyHitAnim = Animator.StringToHash("BodyHit");
    private static readonly int bodyHitDeathAnim = Animator.StringToHash("BodyHitDeath");
    private static readonly int headHitDeathAnim = Animator.StringToHash("HeadShotDeath");

    private void Awake()
    {
        Debug.Assert(animator, "ASSIGN animator TO EnemyMeshController COMPONENT ON " + this.gameObject);

    }


    //Picks a random int and sets the corresoding walk animation
    public void InitWalkAnimation()
    {
        //Get server to pick at random which walk anim to player and all players will use the same
        if (HasStateAuthority)
            walkAnimationValue = Random.Range(0, 3);

        animator.SetFloat(walkAnim, walkAnimationValue);
    }

    public void ResetMesh()
    {
        StopCrawlState();
        animator.SetBool(isDeadAnim, false);
        meshRenderer.gameObject.SetActive(true);

        headBone.localScale = Vector3.one;
        rightArmBone.localScale = Vector3.one;
        leftArmBone.localScale = Vector3.one;
        rightLegBone.localScale = Vector3.one;
        leftLegBone.localScale = Vector3.one;

        //Reset position and rotation of mesh on spawn
        transform.SetLocalPositionAndRotation(Vector3.zero, Quaternion.identity);

        // Note: Hitbox enabling is now handled by UpdateHitboxState() in FixedUpdateNetwork()
        // This ensures proper network synchronization
        DisableAllVFX();

        InitWalkAnimation();

        if (activeVFX != null)
        {
            if(activeVFX.activeInHierarchy)
                activeVFX.SetActive(false);
        }
    }

    private void DisableAllVFX()
    {
        if (headVFX != null)
            headVFX.SetActive(false);
            if (rightArmVFX != null)
            rightArmVFX.SetActive(false);
            if (leftArmVFX != null)
            leftArmVFX.SetActive(false);
            if (rightLegVFX != null)
            rightLegVFX.SetActive(false);
            if (leftLegVFX != null)
            leftLegVFX.SetActive(false);
            if (bodyExplosionVFX != null)
            bodyExplosionVFX.SetActive(false);

    }

    public void DoLimbDamage(HitData hit)
    {


        switch (hit.BodyPart)
        {


            case HitBodyPart.Head:

                //Only desotry head if its a fatal shot
                if (hit.IsFatal)
                {
                    AffectLimb(headBone,headVFX);
                    animator.SetTrigger(headHitDeathAnim);
                }
                else
                {
                    animator.SetTrigger(bodyHitAnim);
                }


                break;
            case HitBodyPart.RightArm:
                AffectLimb(rightArmBone,rightArmVFX);
                if (rightArmVFX != null) rightArmVFX.SetActive(true);
                DisableHitBoxArray(rightArmHitBox);
                animator.SetTrigger(rightArmHitAnim);
                break;
            case HitBodyPart.LeftArm:
                AffectLimb(leftArmBone ,leftArmVFX);
                DisableHitBoxArray(leftArmHitBox);
                if (leftArmVFX != null) leftArmVFX.SetActive(true);
                animator.SetTrigger(leftArmHitAnim);
                break;
            case HitBodyPart.RightLeg:
                AffectLimb(rightLegBone,rightLegVFX);
                if (rightLegVFX != null) rightLegVFX.SetActive(true);
                DisableHitBoxArray(rightLegHitBox);
                StartCrawlState();
                break;
            case HitBodyPart.LeftLeg:
                AffectLimb(leftLegBone,leftLegVFX);
                if (leftLegVFX != null) leftLegVFX.SetActive(true);
                DisableHitBoxArray(leftLegHitBox);
                StartCrawlState();
                break;
            case HitBodyPart.Body:
                if(hit.IsFatal) {
                    animator.SetTrigger(bodyHitDeathAnim);

                    //We then call DoBodyExplosion via an animation event which is triggered by BodyHitDeathAnim



                }
                else
                    animator.SetTrigger(bodyHitAnim);

                break;
            default:
                break;
        }

    }


/// <summary>
/// This gets called by an animation event on the death animaitons
/// </summary>
    private void DoBodyExplosion()
    {
        //Hide body mesh, Show explison VFX and disable hitboxes
        meshRenderer.gameObject.SetActive(false);
  //Set the VFX to the position of the bone
        bodyExplosionVFX.transform.parent = null;
        bodyExplosionVFX.transform.position = bodyBone.position;
        bodyExplosionVFX.SetActive(true);


    }

    private void AffectLimb(Transform bone, GameObject vfx)
    {

        // 0 the scale of bone so it looks hidden
        bone.localScale = Vector3.zero;
        transform.SetLocalPositionAndRotation(Vector3.zero, Quaternion.identity);

        //Set the VFX to the position of the bone
        vfx.transform.parent = null;
        vfx.transform.position = bone.position;
        vfx.SetActive(true);
        activeVFX = vfx;

    }



    private void StartCrawlState()
    {
        animator.SetBool(isCrawlStateAnim, true);
        animator.SetTrigger(legHitAnim);


    }

    private void StopCrawlState()
    {
        animator.SetBool(isCrawlStateAnim, false);


    }

    public void SetIsDead()
    {
      animator.SetBool(isDeadAnim, true);


        DisableHitBoxes();
    }


    private void DisableHitBoxes()
    {
        DisableHitBoxArray(headHitBoxes);
        DisableHitBoxArray(rightArmHitBox);
        DisableHitBoxArray(leftArmHitBox);
        DisableHitBoxArray(rightLegHitBox);
        DisableHitBoxArray(leftLegHitBox);
        DisableHitBoxArray(bodyHitBox);
    }

    private void EnableHitBoxes()
    {
        EnableHitBoxArray(headHitBoxes);
        EnableHitBoxArray(rightArmHitBox);
        EnableHitBoxArray(leftArmHitBox);
        EnableHitBoxArray(rightLegHitBox);
        EnableHitBoxArray(leftLegHitBox);
        EnableHitBoxArray(bodyHitBox);
    }

    /// <summary>
    /// Updates hitbox state based on whether the enemy is alive.
    /// This should be called every network frame to ensure proper synchronization.
    /// </summary>
    /// <param name="isAlive">Whether the enemy is currently alive</param>
    public void UpdateHitboxState(bool isAlive)
    {
        // Only update if state has changed to avoid unnecessary operations
        bool currentlyEnabled = AreHitboxesEnabled();

        if (isAlive && !currentlyEnabled)
        {
            EnableHitBoxes();
            Debug.Log($"<color=cyan>Enabled hitboxes for {gameObject.name} - Health: {GetComponent<Health>()?.CurrentHealth}</color>");
        }
        else if (!isAlive && currentlyEnabled)
        {
            DisableHitBoxes();
            Debug.Log($"<color=red>Disabled hitboxes for {gameObject.name} - Health: {GetComponent<Health>()?.CurrentHealth}</color>");
        }

        // Debug log every few frames to track state
        if (Time.fixedTime % 2f < Time.fixedDeltaTime) // Log every 2 seconds
        {
            Debug.Log($"<color=white>Hitbox state check for {gameObject.name}: isAlive={isAlive}, currentlyEnabled={currentlyEnabled}, Health={GetComponent<Health>()?.CurrentHealth}</color>");
        }
    }

    /// <summary>
    /// Checks if hitboxes are currently enabled by testing the first available hitbox
    /// </summary>
    /// <returns>True if hitboxes are enabled</returns>
    private bool AreHitboxesEnabled()
    {
        // Check the first available hitbox to determine current state
        if (headHitBoxes != null && headHitBoxes.Length > 0 && headHitBoxes[0] != null)
        {
            bool isActive = headHitBoxes[0].HitboxActive;
            bool gameObjectActive = headHitBoxes[0].gameObject.activeInHierarchy;
            Debug.Log($"<color=magenta>Hitbox check for {gameObject.name}: HitboxActive={isActive}, GameObject.active={gameObjectActive}</color>");
            return isActive && gameObjectActive;
        }
        if (bodyHitBox != null && bodyHitBox.Length > 0 && bodyHitBox[0] != null)
        {
            bool isActive = bodyHitBox[0].HitboxActive;
            bool gameObjectActive = bodyHitBox[0].gameObject.activeInHierarchy;
            Debug.Log($"<color=magenta>Hitbox check for {gameObject.name}: HitboxActive={isActive}, GameObject.active={gameObjectActive}</color>");
            return isActive && gameObjectActive;
        }
        Debug.Log($"<color=magenta>No hitboxes found for {gameObject.name}</color>");
        return false;
    }

    /// <summary>
    /// Disables all hitboxes in the provided array
    /// </summary>
    /// <param name="hitboxes">Array of hitboxes to disable</param>
    public void DisableHitBoxArray(Hitbox[] hitboxes)
    {
        if (hitboxes == null || hitboxes.Length == 0)
            return;

        foreach (var hitbox in hitboxes)
        {
            if (hitbox != null)
               { hitbox.gameObject.SetActive(false);
                hitbox.HitboxActive = false;
               }


        }
    }

    /// <summary>
    /// Disables specific hitboxes by their array reference
    /// </summary>
    /// <param name="hitboxesToDisable">Array of hitbox arrays to disable</param>
    public void DisableSpecificHitBoxes(Hitbox[][] hitboxesToDisable)
    {
        if (hitboxesToDisable == null || hitboxesToDisable.Length == 0)
            return;

        foreach (var hitboxArray in hitboxesToDisable)
        {
            DisableHitBoxArray(hitboxArray);
        }
    }

    /// <summary>
    /// Enables all hitboxes in the provided array
    /// </summary>
    /// <param name="hitboxes">Array of hitboxes to enable</param>
    private void EnableHitBoxArray(Hitbox[] hitboxes)
    {
        if (hitboxes == null || hitboxes.Length == 0)
            return;

        foreach (var hitbox in hitboxes)
        {
            if (hitbox != null)
            {
                Debug.Log($"<color=lime>Enabling hitbox {hitbox.name} on {gameObject.name} - Before: HitboxActive={hitbox.HitboxActive}, GameObject.active={hitbox.gameObject.activeInHierarchy}</color>");

                hitbox.gameObject.SetActive(true);
                hitbox.HitboxActive = true;

                Debug.Log($"<color=lime>Enabled hitbox {hitbox.name} on {gameObject.name} - After: HitboxActive={hitbox.HitboxActive}, GameObject.active={hitbox.gameObject.activeInHierarchy}</color>");
            }
        }
    }
}
