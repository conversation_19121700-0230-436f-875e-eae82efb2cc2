%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 350c58cb46cd15b4cbb0b64e08f3c177, type: 3}
  m_Name: DefaultWaveConfig
  m_EditorClassIdentifier: 
  waves:
  - waveName: Wave 1
    baseEnemyCount: 5
    enemyCountMultiplier: 1
    timeBetweenSpawns: 1
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 2
    baseEnemyCount: 8
    enemyCountMultiplier: 1.1
    timeBetweenSpawns: 0.95
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 3
    baseEnemyCount: 11
    enemyCountMultiplier: 1.2
    timeBetweenSpawns: 0.9
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 4
    baseEnemyCount: 14
    enemyCountMultiplier: 1.3
    timeBetweenSpawns: 0.85
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 5
    baseEnemyCount: 17
    enemyCountMultiplier: 1.4
    timeBetweenSpawns: 0.8
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 6
    baseEnemyCount: 20
    enemyCountMultiplier: 1.5
    timeBetweenSpawns: 0.75
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 7
    baseEnemyCount: 23
    enemyCountMultiplier: 1.6
    timeBetweenSpawns: 0.7
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 8
    baseEnemyCount: 26
    enemyCountMultiplier: 1.7
    timeBetweenSpawns: 0.65
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 9
    baseEnemyCount: 29
    enemyCountMultiplier: 1.8
    timeBetweenSpawns: 0.6
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  - waveName: Wave 10
    baseEnemyCount: 32
    enemyCountMultiplier: 1.9
    timeBetweenSpawns: 0.55
    enemies:
    - enemyName: Basic Enemy
      enemyPrefab:
        RawGuidValue: ********************************
      spawnWeight: 1
      difficultyScaling: 0.1
    cooldownAfterWave: 2
    difficultyIncrease: 0.1
  isEndless: 0
  endlessStartWave: 5
  globalDifficultyMultiplier: 1
