%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &175081899550319689
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2636330705734564511}
  - component: {fileID: 7745993261615764638}
  - component: {fileID: 4767208554819820468}
  - component: {fileID: 5840563491051273879}
  m_Layer: 0
  m_Name: FakeShadow (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2636330705734564511
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175081899550319689}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710707, z: -0, w: 0.70710653}
  m_LocalPosition: {x: 10.6754, y: 0.0001, z: -2.6041}
  m_LocalScale: {x: 0.78754526, y: 1, z: 0.36760277}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &7745993261615764638
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175081899550319689}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &4767208554819820468
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175081899550319689}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5840563491051273879
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175081899550319689}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &476066717390734076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8252627075717972114}
  - component: {fileID: 222634957845853740}
  - component: {fileID: 4896772180460267750}
  - component: {fileID: 3337645097209031800}
  m_Layer: 0
  m_Name: FakeShadow (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8252627075717972114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 476066717390734076}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 7.87, y: 0.0001, z: -17.84}
  m_LocalScale: {x: 1.773326, y: 1, z: 0.7622313}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &222634957845853740
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 476066717390734076}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &4896772180460267750
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 476066717390734076}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3337645097209031800
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 476066717390734076}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &539907516757296545
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5490324804074966031}
  - component: {fileID: 8536565495264376500}
  - component: {fileID: 1985431685024865043}
  - component: {fileID: 7980565158211568000}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5490324804074966031
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 539907516757296545}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 32.98, y: 0.8778, z: 2.687}
  m_LocalScale: {x: 1.1798297, y: 2.0039616, z: 0.32435283}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8536565495264376500
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 539907516757296545}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1985431685024865043
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 539907516757296545}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7980565158211568000
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 539907516757296545}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &620947982484039880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6047784756179861630}
  - component: {fileID: 1017333472767000772}
  m_Layer: 0
  m_Name: EnemySpawnPoint (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6047784756179861630
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 620947982484039880}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.70710707, z: -0, w: 0.70710653}
  m_LocalPosition: {x: -13.14, y: 0, z: -5.14}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!114 &1017333472767000772
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 620947982484039880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &772006769500281430
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3568709660607474595}
  - component: {fileID: 8415579410773622855}
  m_Layer: 0
  m_Name: EnemySpawnPoint (12)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3568709660607474595
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 772006769500281430}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 16.17, y: 0, z: 7.72}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8415579410773622855
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 772006769500281430}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &939366642565380331
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8016088233093785906}
  - component: {fileID: 2492663044464259532}
  - component: {fileID: 1675353520416559365}
  - component: {fileID: 2204292097443885748}
  m_Layer: 0
  m_Name: SpawnFloor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8016088233093785906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939366642565380331}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 33.75, y: 0, z: -16.26}
  m_LocalScale: {x: 0.58458, y: 0.58458, z: 0.58458}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2492663044464259532
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939366642565380331}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1675353520416559365
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939366642565380331}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &2204292097443885748
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 939366642565380331}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &992966312079674847
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 860928143088377144}
  - component: {fileID: 94630486459914925}
  - component: {fileID: 918012472711246152}
  - component: {fileID: 7425146155458960808}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &860928143088377144
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992966312079674847}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 24.7564, y: 0.8778, z: -8.36}
  m_LocalScale: {x: 1.1798297, y: 2.0039616, z: 0.32435283}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &94630486459914925
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992966312079674847}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &918012472711246152
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992966312079674847}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7425146155458960808
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992966312079674847}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &1042656197780459604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4733967648757096331}
  - component: {fileID: 9150726768344040696}
  - component: {fileID: 963014824295674118}
  m_Layer: 0
  m_Name: Particle System
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4733967648757096331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042656197780459604}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 1.384, y: 0.362, z: 10.845}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!198 &9150726768344040696
ParticleSystem:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042656197780459604}
  serializedVersion: 8
  lengthInSec: 3
  simulationSpeed: 1
  stopAction: 0
  cullingMode: 0
  ringBufferMode: 0
  ringBufferLoopRange: {x: 0, y: 1}
  emitterVelocityMode: 1
  looping: 1
  prewarm: 1
  playOnAwake: 1
  useUnscaledTime: 0
  autoRandomSeed: 1
  startDelay:
    serializedVersion: 2
    minMaxState: 0
    scalar: 0
    minScalar: 0
    maxCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    minCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      - serializedVersion: 3
        time: 1
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0.33333334
        outWeight: 0.33333334
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  moveWithTransform: 0
  moveWithCustomTransform: {fileID: 0}
  scalingMode: 1
  randomSeed: 0
  InitialModule:
    serializedVersion: 3
    enabled: 1
    startLifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 2
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 5
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startColor:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    startSize:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.65
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startSizeZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotationY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startRotation:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    randomizeRotationDirection: 0
    gravitySource: 0
    maxNumParticles: 20
    customEmitterVelocity: {x: 0, y: 0, z: 0}
    size3D: 0
    rotation3D: 0
    gravityModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  ShapeModule:
    serializedVersion: 6
    enabled: 1
    type: 4
    angle: 0
    length: 5
    boxThickness: {x: 0, y: 0, z: 0}
    radiusThickness: 1
    donutRadius: 0.2
    m_Position: {x: 0, y: 0, z: 0}
    m_Rotation: {x: 0, y: 0, z: 0}
    m_Scale: {x: 1, y: 1, z: 1}
    placementMode: 0
    m_MeshMaterialIndex: 0
    m_MeshNormalOffset: 0
    m_MeshSpawn:
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    m_Mesh: {fileID: 0}
    m_MeshRenderer: {fileID: 0}
    m_SkinnedMeshRenderer: {fileID: 0}
    m_Sprite: {fileID: 0}
    m_SpriteRenderer: {fileID: 0}
    m_UseMeshMaterialIndex: 0
    m_UseMeshColors: 1
    alignToDirection: 0
    m_Texture: {fileID: 0}
    m_TextureClipChannel: 3
    m_TextureClipThreshold: 0
    m_TextureUVChannel: 0
    m_TextureColorAffectsParticles: 1
    m_TextureAlphaAffectsParticles: 1
    m_TextureBilinearFiltering: 0
    randomDirectionAmount: 0
    sphericalDirectionAmount: 0
    randomPositionAmount: 0
    radius:
      value: 0.5
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
    arc:
      value: 180
      mode: 0
      spread: 0
      speed:
        serializedVersion: 2
        minMaxState: 0
        scalar: 1
        minScalar: 1
        maxCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        minCurve:
          serializedVersion: 2
          m_Curve:
          - serializedVersion: 3
            time: 0
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          - serializedVersion: 3
            time: 1
            value: 1
            inSlope: 0
            outSlope: 0
            tangentMode: 0
            weightedMode: 0
            inWeight: 0.33333334
            outWeight: 0.33333334
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
  EmissionModule:
    enabled: 1
    serializedVersion: 4
    rateOverTime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 10
      minScalar: 10
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rateOverDistance:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_BurstCount: 0
    m_Bursts: []
  SizeModule:
    enabled: 1
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: -0.145032
          outSlope: -0.145032
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.30939233
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  RotationModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
  ColorModule:
    enabled: 1
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 0.23400003, b: 0.23400003, a: 1}
        key1: {r: 1, g: 0.80982494, b: 0, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0.5486928}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 56283
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 15806
        atime2: 39128
        atime3: 54741
        atime4: 54741
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: 0
        m_NumColorKeys: 2
        m_NumAlphaKeys: 4
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  UVModule:
    serializedVersion: 2
    enabled: 0
    mode: 0
    timeMode: 0
    fps: 30
    frameOverTime:
      serializedVersion: 2
      minMaxState: 1
      scalar: 0.9999
      minScalar: 0.9999
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    startFrame:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedRange: {x: 0, y: 1}
    tilesX: 1
    tilesY: 1
    animationType: 0
    rowIndex: 0
    cycles: 1
    uvChannelMask: -1
    rowMode: 1
    sprites:
    - sprite: {fileID: 0}
    flipU: 0
    flipV: 0
  VelocityModule:
    enabled: 1
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalY:
      serializedVersion: 2
      minMaxState: 0
      scalar: -0.1
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: -0.1
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetX:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.1
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    orbitalOffsetZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.1
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    radial:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    speedModifier:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
  InheritVelocityModule:
    enabled: 0
    m_Mode: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  LifetimeByEmitterSpeedModule:
    enabled: 0
    m_Curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: -0.8
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0.2
          inSlope: -0.8
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Range: {x: 0, y: 1}
  ForceModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    inWorldSpace: 0
    randomizePerFrame: 0
  ExternalForcesModule:
    serializedVersion: 2
    enabled: 0
    multiplierCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    influenceFilter: 0
    influenceMask:
      serializedVersion: 2
      m_Bits: 4294967295
    influenceList: []
  ClampVelocityModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    magnitude:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxis: 0
    inWorldSpace: 0
    multiplyDragByParticleSize: 1
    multiplyDragByParticleVelocity: 1
    dampen: 0
    drag:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  NoiseModule:
    enabled: 0
    strength:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthY:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    strengthZ:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    frequency: 0.5
    damping: 1
    octaves: 1
    octaveMultiplier: 0.5
    octaveScale: 2
    quality: 1
    scrollSpeed:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remap:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapY:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapZ:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: -1
          inSlope: 0
          outSlope: 2
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 2
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    remapEnabled: 0
    positionAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    rotationAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    sizeAmount:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
  SizeBySpeedModule:
    enabled: 0
    curve:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    z:
      serializedVersion: 2
      minMaxState: 1
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 1
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 1
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    range: {x: 0, y: 1}
    separateAxes: 0
  RotationBySpeedModule:
    enabled: 0
    x:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    y:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    curve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0.7853982
      minScalar: 0.7853982
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    separateAxes: 0
    range: {x: 0, y: 1}
  ColorBySpeedModule:
    enabled: 0
    gradient:
      serializedVersion: 2
      minMaxState: 1
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    range: {x: 0, y: 1}
  CollisionModule:
    enabled: 0
    serializedVersion: 4
    type: 0
    collisionMode: 0
    colliderForce: 0
    multiplyColliderForceByParticleSize: 0
    multiplyColliderForceByParticleSpeed: 0
    multiplyColliderForceByCollisionAngle: 1
    m_Planes: []
    m_Dampen:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_Bounce:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    m_EnergyLossOnCollision:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minKillSpeed: 0
    maxKillSpeed: 10000
    radiusScale: 1
    collidesWith:
      serializedVersion: 2
      m_Bits: 4294967295
    maxCollisionShapes: 256
    quality: 0
    voxelSize: 0.5
    collisionMessages: 0
    collidesWithDynamic: 1
    interiorCollisions: 0
  TriggerModule:
    enabled: 0
    serializedVersion: 2
    inside: 1
    outside: 0
    enter: 0
    exit: 0
    colliderQueryMode: 0
    radiusScale: 1
    primitives: []
  SubModule:
    serializedVersion: 2
    enabled: 0
    subEmitters:
    - serializedVersion: 3
      emitter: {fileID: 0}
      type: 0
      properties: 0
      emitProbability: 1
  LightsModule:
    enabled: 0
    ratio: 0
    light: {fileID: 0}
    randomDistribution: 1
    color: 1
    range: 1
    intensity: 1
    rangeCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    intensityCurve:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    maxLights: 20
  TrailModule:
    enabled: 0
    mode: 0
    ratio: 1
    lifetime:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    minVertexDistance: 0.2
    textureMode: 0
    textureScale: {x: 1, y: 1}
    ribbonCount: 1
    shadowBias: 0.5
    worldSpace: 0
    dieWithParticles: 1
    sizeAffectsWidth: 1
    sizeAffectsLifetime: 0
    inheritParticleColor: 1
    generateLightingData: 0
    splitSubEmitterRibbons: 0
    attachRibbonsToTransform: 0
    colorOverLifetime:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    widthOverTrail:
      serializedVersion: 2
      minMaxState: 0
      scalar: 1
      minScalar: 1
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    colorOverTrail:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
  CustomDataModule:
    enabled: 0
    mode0: 0
    vectorComponentCount0: 4
    color0:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel0: Color
    vector0_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_0: X
    vector0_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_1: Y
    vector0_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_2: Z
    vector0_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel0_3: W
    mode1: 0
    vectorComponentCount1: 4
    color1:
      serializedVersion: 2
      minMaxState: 0
      minColor: {r: 1, g: 1, b: 1, a: 1}
      maxColor: {r: 1, g: 1, b: 1, a: 1}
      maxGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
      minGradient:
        serializedVersion: 2
        key0: {r: 1, g: 1, b: 1, a: 1}
        key1: {r: 1, g: 1, b: 1, a: 1}
        key2: {r: 0, g: 0, b: 0, a: 0}
        key3: {r: 0, g: 0, b: 0, a: 0}
        key4: {r: 0, g: 0, b: 0, a: 0}
        key5: {r: 0, g: 0, b: 0, a: 0}
        key6: {r: 0, g: 0, b: 0, a: 0}
        key7: {r: 0, g: 0, b: 0, a: 0}
        ctime0: 0
        ctime1: 65535
        ctime2: 0
        ctime3: 0
        ctime4: 0
        ctime5: 0
        ctime6: 0
        ctime7: 0
        atime0: 0
        atime1: 65535
        atime2: 0
        atime3: 0
        atime4: 0
        atime5: 0
        atime6: 0
        atime7: 0
        m_Mode: 0
        m_ColorSpace: -1
        m_NumColorKeys: 2
        m_NumAlphaKeys: 2
    colorLabel1: Color
    vector1_0:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_0: X
    vector1_1:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_1: Y
    vector1_2:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_2: Z
    vector1_3:
      serializedVersion: 2
      minMaxState: 0
      scalar: 0
      minScalar: 0
      maxCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      minCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        - serializedVersion: 3
          time: 1
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0.33333334
          outWeight: 0.33333334
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
    vectorLabel1_3: W
--- !u!199 &963014824295674118
ParticleSystemRenderer:
  serializedVersion: 6
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1042656197780459604}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 352e894f700b75644a2dcf9cf23aad05, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_RenderMode: 0
  m_MeshDistribution: 0
  m_SortMode: 0
  m_MinParticleSize: 0
  m_MaxParticleSize: 0.5
  m_CameraVelocityScale: 0
  m_VelocityScale: 0
  m_LengthScale: 2
  m_SortingFudge: 0
  m_NormalDirection: 1
  m_ShadowBias: 0
  m_RenderAlignment: 0
  m_Pivot: {x: 0, y: 0, z: 0}
  m_Flip: {x: 0, y: 0, z: 0}
  m_EnableGPUInstancing: 1
  m_ApplyActiveColorSpace: 1
  m_AllowRoll: 1
  m_FreeformStretching: 0
  m_RotateWithStretchDirection: 1
  m_UseCustomVertexStreams: 0
  m_VertexStreams: 00010304
  m_UseCustomTrailVertexStreams: 0
  m_TrailVertexStreams: 00010304
  m_Mesh: {fileID: 0}
  m_Mesh1: {fileID: 0}
  m_Mesh2: {fileID: 0}
  m_Mesh3: {fileID: 0}
  m_MeshWeighting: 1
  m_MeshWeighting1: 1
  m_MeshWeighting2: 1
  m_MeshWeighting3: 1
  m_MaskInteraction: 0
--- !u!1 &1640738372612739575
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1286551046039288994}
  - component: {fileID: 1907733083432369008}
  - component: {fileID: 8708646616135827809}
  m_Layer: 0
  m_Name: Point Light (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1286551046039288994
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640738372612739575}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.273, y: 7.124, z: -6.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1907733083432369008
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640738372612739575}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 7.26
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &8708646616135827809
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1640738372612739575}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &2099304524981447894
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7583707845605896319}
  - component: {fileID: 8490091925444774639}
  m_Layer: 0
  m_Name: EnemySpawnPoint (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7583707845605896319
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2099304524981447894}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 7.97, y: 0, z: -1.82}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8490091925444774639
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2099304524981447894}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2325184004758434878
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5939629317926678396}
  - component: {fileID: 3276065341078390662}
  - component: {fileID: 4560075460306224354}
  - component: {fileID: 1300202858877322284}
  m_Layer: 0
  m_Name: FakeShadow (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5939629317926678396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325184004758434878}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.49, y: 0.0001, z: -14.87}
  m_LocalScale: {x: 1.6772385, y: 1, z: 1.0968935}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3276065341078390662
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325184004758434878}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &4560075460306224354
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325184004758434878}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1300202858877322284
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325184004758434878}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2365336670244779327
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2789177862312071552}
  - component: {fileID: 5223807100454430748}
  m_Layer: 0
  m_Name: EnemySpawnPoint (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2789177862312071552
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2365336670244779327}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 32.63, y: 0, z: 8.6}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5223807100454430748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2365336670244779327}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2431804050866903914
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1052517946502548546}
  - component: {fileID: 3235583672008890505}
  - component: {fileID: 2859993744054277504}
  - component: {fileID: 5950189452250200963}
  m_Layer: 0
  m_Name: FakeShadow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1052517946502548546
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2431804050866903914}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.38, y: 0.0001, z: -0.29}
  m_LocalScale: {x: 1.6772385, y: 1, z: 1.0968935}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3235583672008890505
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2431804050866903914}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &2859993744054277504
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2431804050866903914}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5950189452250200963
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2431804050866903914}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2728512471375074124
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2728550411606789951}
  - component: {fileID: 6519015852573038800}
  - component: {fileID: 4199228620871376942}
  - component: {fileID: 4522156955971352026}
  m_Layer: 0
  m_Name: FakeShadow (8)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2728550411606789951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728512471375074124}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.44862235, z: -0, w: 0.89372146}
  m_LocalPosition: {x: 14.81, y: 0.0001, z: -5.23}
  m_LocalScale: {x: 0.8523909, y: 1, z: 0.68612474}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 53.311, z: 0}
--- !u!33 &6519015852573038800
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728512471375074124}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &4199228620871376942
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728512471375074124}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4522156955971352026
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2728512471375074124}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2809271208360642401
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8045176283678142228}
  - component: {fileID: 3637895726744130882}
  - component: {fileID: 4949276411319610338}
  - component: {fileID: 2807660675723085275}
  - component: {fileID: 8375210976489372828}
  m_Layer: 0
  m_Name: '[BuildingBlock] Spatial Audio_Garden'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8045176283678142228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2809271208360642401}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 25.65, y: 0, z: -0.77}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3637895726744130882
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2809271208360642401}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b023183069df224080314a560e6e1ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  blockId: 6737262b-4fab-4779-a38b-fe74012fa505
  instanceId: 6d49b6ee-a8f5-4327-b088-935a8954ea4b
  version: 1
  installationRoutineCheckpoint:
    _installationRoutineId: 
    _installationVariants: []
--- !u!82 &4949276411319610338
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2809271208360642401}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 31ba0d2da59f843459f5a1381787c8af, type: 3}
  m_Resource: {fileID: 8300000, guid: 31ba0d2da59f843459f5a1381787c8af, type: 3}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 1
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 17.0255
  Pan2D: 0
  rolloffMode: 2
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0.25392723
      value: 1.0038605
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.49044427
      value: 0.70328784
      inSlope: -2.0293899
      outSlope: -2.0293899
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.6565169
      value: 0.24028543
      inSlope: -1.9027256
      outSlope: -1.9027256
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.8926697
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &2807660675723085275
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2809271208360642401}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a07f270ac36c49789466c75ceb88424c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableSpatialization: 1
  gainBoostDb: 0
  enableAcoustics: 1
  reverbSendDb: 0
--- !u!114 &8375210976489372828
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2809271208360642401}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 796f04c5a94947649c2235acef0008ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hrtfIntensity: 1
  volumetricRadius: 2.37
  earlyReflectionsSendDb: 0
  reverbReach: 0.5
  occlusionIntensity: 1
  directivityIntensity: 1
  directivityPattern: 0
  directSoundEnabled: 1
  mediumAbsorption: 1
--- !u!1 &3015387652424831304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6752928572488711752}
  - component: {fileID: 8865806116233420360}
  - component: {fileID: 7566423476988432710}
  m_Layer: 0
  m_Name: Point Light (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6752928572488711752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3015387652424831304}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -7.69, y: 4.95, z: -12.78}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &8865806116233420360
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3015387652424831304}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 4
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &7566423476988432710
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3015387652424831304}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &3028889882658966982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5570714371743417333}
  - component: {fileID: 2607741429565682174}
  - component: {fileID: 3681148055058341019}
  m_Layer: 0
  m_Name: Point Light (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5570714371743417333
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028889882658966982}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.273, y: 7.124, z: 5.206}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &2607741429565682174
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028889882658966982}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 7.26
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &3681148055058341019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3028889882658966982}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &3129783391277178055
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5151956065207037864}
  - component: {fileID: 8062407683635219533}
  - component: {fileID: 6724866283619945132}
  m_Layer: 0
  m_Name: Moon_Rim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5151956065207037864
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3129783391277178055}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 53.96, y: 40.6, z: 24.82}
  m_LocalScale: {x: 6.7, y: 6.7, z: 6.7}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8062407683635219533
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3129783391277178055}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6724866283619945132
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3129783391277178055}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8e0725ca171884a4892999f833a86802, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3226868361049833729
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6095957924780535562}
  - component: {fileID: 4430974566496354243}
  m_Layer: 0
  m_Name: EnemySpawnPoint (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6095957924780535562
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3226868361049833729}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -3.862, y: 0, z: -21.187}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4430974566496354243
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3226868361049833729}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3295536963130237140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8580674101099891617}
  - component: {fileID: 4345476380153751139}
  - component: {fileID: 4666266027183168128}
  - component: {fileID: 6694478536691776515}
  m_Layer: 12
  m_Name: Spawn_PlayerBlocker (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8580674101099891617
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3295536963130237140}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710576, z: -0, w: 0.70710784}
  m_LocalPosition: {x: -8.395, y: 1.706, z: -5.3568}
  m_LocalScale: {x: 3.6761446, y: 3.4125302, z: 0.1929502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &4345476380153751139
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3295536963130237140}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4666266027183168128
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3295536963130237140}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8a50d59e4a6462f47afb9415b9e77764, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6694478536691776515
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3295536963130237140}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 4096
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 12287
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &3369470890253600451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3190545107113384260}
  - component: {fileID: 6755394632128175370}
  m_Layer: 0
  m_Name: Occlusion Area
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3190545107113384260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369470890253600451}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!192 &6755394632128175370
OcclusionArea:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3369470890253600451}
  serializedVersion: 2
  m_Size: {x: 47.289307, y: 18.198004, z: 34.81443}
  m_Center: {x: 13.839453, y: 8.349757, z: -5.109549}
  m_IsViewVolume: 1
--- !u!1 &3665647843214859302
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6747113880355697210}
  - component: {fileID: 6076741183567614953}
  m_Layer: 0
  m_Name: EnemySpawnPoint (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6747113880355697210
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3665647843214859302}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -5.46, y: 0, z: -6.92}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6076741183567614953
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3665647843214859302}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3915436454687128668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4116316361349432036}
  - component: {fileID: 6947481094001718835}
  - component: {fileID: 6525780599642970357}
  m_Layer: 0
  m_Name: WallCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4116316361349432036
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3915436454687128668}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 12.1254, y: 8.5534, z: -13.1516}
  m_LocalScale: {x: 0.48982856, y: 17.0872, z: 1.1145867}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6947481094001718835
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3915436454687128668}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &6525780599642970357
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3915436454687128668}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &4346764945010050609
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8131728787153880412}
  - component: {fileID: 2460526981576300155}
  - component: {fileID: 7267606021992438281}
  - component: {fileID: 7102330880325083945}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &8131728787153880412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4346764945010050609}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 21.9164, y: 0.8778, z: -6.7896}
  m_LocalScale: {x: 2.8071132, y: 2.0039616, z: 1.6906117}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2460526981576300155
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4346764945010050609}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7267606021992438281
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4346764945010050609}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &7102330880325083945
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4346764945010050609}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &5115047830649274831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8556256156705537812}
  - component: {fileID: 2238676023459860527}
  - component: {fileID: 6974741525419988836}
  - component: {fileID: 4298804326647744432}
  m_Layer: 0
  m_Name: FakeShadow (9)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8556256156705537812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5115047830649274831}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.90495074, z: -0, w: 0.42551643}
  m_LocalPosition: {x: 15.1, y: 0.0001, z: 0.99}
  m_LocalScale: {x: 0.85239094, y: 1, z: 0.68612474}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 129.633, z: 0}
--- !u!33 &2238676023459860527
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5115047830649274831}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &6974741525419988836
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5115047830649274831}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4298804326647744432
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5115047830649274831}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5278621720096765281
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6987082066359882212}
  - component: {fileID: 509669451981349972}
  m_Layer: 0
  m_Name: EnemySpawnPoint (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6987082066359882212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5278621720096765281}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 33.75, y: 0, z: -15.43}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &509669451981349972
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5278621720096765281}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &5363764506145211087
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7233511749282496632}
  - component: {fileID: 4834861686984898432}
  - component: {fileID: 9094941073986551238}
  - component: {fileID: 4902085197402445556}
  m_Layer: 0
  m_Name: FakeShadow (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7233511749282496632
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5363764506145211087}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710707, z: -0, w: 0.70710653}
  m_LocalPosition: {x: 10.94, y: 0.0001, z: 2.02}
  m_LocalScale: {x: 0.6509782, y: 1, z: 0.31467137}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &4834861686984898432
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5363764506145211087}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &9094941073986551238
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5363764506145211087}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4902085197402445556
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5363764506145211087}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5526816467031415715
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5886703690684333848}
  - component: {fileID: 1786870903223623655}
  - component: {fileID: 6440391283158460724}
  - component: {fileID: 3384277560260993948}
  m_Layer: 0
  m_Name: FakeShadow (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5886703690684333848
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5526816467031415715}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 20.65, y: 0.0001, z: -7.02}
  m_LocalScale: {x: 0.3303015, y: 1, z: 0.6861247}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1786870903223623655
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5526816467031415715}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &6440391283158460724
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5526816467031415715}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3384277560260993948
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5526816467031415715}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &5580997206256473703
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8884843353653538816}
  - component: {fileID: 2007152586798220758}
  - component: {fileID: 2571164566603229833}
  m_Layer: 0
  m_Name: WallCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8884843353653538816
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5580997206256473703}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 12.1254, y: 8.5534, z: -1.0476}
  m_LocalScale: {x: 0.48982856, y: 17.0872, z: 16.99065}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2007152586798220758
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5580997206256473703}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &2571164566603229833
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5580997206256473703}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &5686354755547948132
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6937297806589867895}
  - component: {fileID: 569274323081415079}
  - component: {fileID: 6795827578671590556}
  - component: {fileID: 8748263685774423043}
  m_Layer: 0
  m_Name: SpawnFloor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6937297806589867895
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5686354755547948132}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -11.81, y: 0, z: -5.3613}
  m_LocalScale: {x: 0.58458, y: 0.58458, z: 0.35840717}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &569274323081415079
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5686354755547948132}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6795827578671590556
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5686354755547948132}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!64 &8748263685774423043
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5686354755547948132}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &5730386501124415754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2826503088417032287}
  m_Layer: 0
  m_Name: FakeContactShadows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2826503088417032287
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5730386501124415754}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1052517946502548546}
  - {fileID: 2728550411606789951}
  - {fileID: 5886703690684333848}
  - {fileID: 4696737340708732992}
  - {fileID: 8556256156705537812}
  - {fileID: 2626358405268086260}
  - {fileID: 8252627075717972114}
  - {fileID: 5939629317926678396}
  - {fileID: 30207903662723112}
  - {fileID: 4714503882549410089}
  - {fileID: 7233511749282496632}
  - {fileID: 2636330705734564511}
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5755968533480403609
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2022534035247153365}
  - component: {fileID: 8671839172182889056}
  m_Layer: 0
  m_Name: EnemySpawnPoint (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2022534035247153365
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5755968533480403609}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -11.12, y: 0, z: -5.26}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!114 &8671839172182889056
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5755968533480403609}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &5782073188769250455
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 391867044807764223}
  m_Layer: 0
  m_Name: TestingDefencePoints
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &391867044807764223
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5782073188769250455}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7131693574153160892}
  - {fileID: 9102120610915810982}
  - {fileID: 263227880829207377}
  - {fileID: 290133258540671478}
  - {fileID: 7688370556310459969}
  - {fileID: 5754719126867090077}
  - {fileID: 939015185716644490}
  - {fileID: 1964617735871407186}
  - {fileID: 3792940842997969828}
  - {fileID: 9097346163268414464}
  - {fileID: 6406126785327268790}
  - {fileID: 2265165540553889292}
  - {fileID: 4245894342024453719}
  - {fileID: 9207385666429255207}
  - {fileID: 2058597774331930469}
  - {fileID: 4644752301355173674}
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5814947555070470834
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8034854943278562967}
  - component: {fileID: 6711444487278355423}
  - component: {fileID: 3623324480363359986}
  - component: {fileID: 3265433326297799713}
  m_Layer: 12
  m_Name: Spawn_PlayerBlocker (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8034854943278562967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5814947555070470834}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -1, z: -0, w: 0.0000029504295}
  m_LocalPosition: {x: -3.7469, y: 1.706, z: -19.714}
  m_LocalScale: {x: 1.9504294, y: 3.4125302, z: 0.1929502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: -180, z: 0}
--- !u!33 &6711444487278355423
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5814947555070470834}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3623324480363359986
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5814947555070470834}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8a50d59e4a6462f47afb9415b9e77764, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3265433326297799713
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5814947555070470834}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 4096
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 12287
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &6031855226309815803
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2880866580636840437}
  - component: {fileID: 4055678569347117228}
  - component: {fileID: 3581612603461687029}
  - component: {fileID: 3819514419547160124}
  m_Layer: 12
  m_Name: Spawn_PlayerBlocker (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2880866580636840437
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6031855226309815803}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710576, z: -0, w: 0.70710784}
  m_LocalPosition: {x: 33.84, y: 1.8125, z: 8.4816}
  m_LocalScale: {x: 3.899074, y: 3.6256406, z: 0.1929502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &4055678569347117228
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6031855226309815803}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3581612603461687029
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6031855226309815803}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8a50d59e4a6462f47afb9415b9e77764, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3819514419547160124
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6031855226309815803}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 4096
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 12287
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &6217422033877270138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1287365713295402976}
  - component: {fileID: 4027749212834913081}
  m_Layer: 0
  m_Name: EnemySpawnPoint (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1287365713295402976
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6217422033877270138}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 33.5, y: 0, z: -17.85}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4027749212834913081
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6217422033877270138}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6676512476124005502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 67076762289157321}
  - component: {fileID: 6723475781801696378}
  m_Layer: 0
  m_Name: EnemySpawnPoint
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &67076762289157321
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6676512476124005502}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710677, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 35.34, y: 0, z: 8.09}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!114 &6723475781801696378
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6676512476124005502}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6700523932546840672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8244563822735463083}
  - component: {fileID: 5349875316542735454}
  - component: {fileID: 5583760253348047123}
  m_Layer: 0
  m_Name: Point Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8244563822735463083
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6700523932546840672}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 11.26, y: 4.85, z: 10.484104}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &5349875316542735454
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6700523932546840672}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 4
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &5583760253348047123
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6700523932546840672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &6909077508671370247
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1587467134798155328}
  - component: {fileID: 1601765283909573766}
  m_Layer: 0
  m_Name: EnemySpawnPoint (10)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1587467134798155328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6909077508671370247}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 22.77, y: 0, z: -10.13}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1601765283909573766
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6909077508671370247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6957387189403883165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7327355912112416471}
  - component: {fileID: 4649445749966156213}
  - component: {fileID: 279224305437800627}
  - component: {fileID: 3675995764305985127}
  m_Layer: 0
  m_Name: SpawnCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7327355912112416471
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6957387189403883165}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710707, z: -0, w: 0.70710653}
  m_LocalPosition: {x: 35.253, y: 0.35, z: 6.231}
  m_LocalScale: {x: 0.175, y: 1.1, z: 4.5052214}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &4649445749966156213
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6957387189403883165}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &279224305437800627
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6957387189403883165}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &3675995764305985127
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6957387189403883165}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &7245102074643206129
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7273606766877193120}
  m_Layer: 0
  m_Name: SpawnLocations
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7273606766877193120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7245102074643206129}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4217398522653141037}
  - {fileID: 4575575712501104559}
  - {fileID: 7991001394602566468}
  - {fileID: 5022503296870343785}
  - {fileID: 8016088233093785906}
  - {fileID: 6937297806589867895}
  - {fileID: 2892683757898821926}
  - {fileID: 7327355912112416471}
  - {fileID: 67076762289157321}
  - {fileID: 6095957924780535562}
  - {fileID: 6987082066359882212}
  - {fileID: 1287365713295402976}
  - {fileID: 2022534035247153365}
  - {fileID: 6047784756179861630}
  - {fileID: 5546768098831521996}
  - {fileID: 2880866580636840437}
  - {fileID: 8580674101099891617}
  - {fileID: 8034854943278562967}
  - {fileID: 1398096061832173629}
  - {fileID: 5837109874993123130}
  - {fileID: 7583707845605896319}
  - {fileID: 6747113880355697210}
  - {fileID: 1587467134798155328}
  - {fileID: 2789177862312071552}
  - {fileID: 3568709660607474595}
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7296058035760239173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5837109874993123130}
  - component: {fileID: 2788071664726444865}
  m_Layer: 0
  m_Name: EnemySpawnPoint (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &5837109874993123130
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7296058035760239173}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.17, y: 0, z: 6.42}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2788071664726444865
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7296058035760239173}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &7334186476216656650
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1398096061832173629}
  - component: {fileID: 5806117144173959084}
  m_Layer: 0
  m_Name: EnemySpawnPoint (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1398096061832173629
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7334186476216656650}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 23.78, y: 0, z: 8.13}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5806117144173959084
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7334186476216656650}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0f5373f7fc192bd4193dac95d40c93fb, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &7514934556191432061
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7459508192986603096}
  - component: {fileID: 5885396649859820719}
  - component: {fileID: 7573721072525014933}
  m_Layer: 0
  m_Name: WallCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7459508192986603096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7514934556191432061}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 12.1254, y: 8.5534, z: 11.3031}
  m_LocalScale: {x: 0.48982856, y: 17.0872, z: 2.960788}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5885396649859820719
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7514934556191432061}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!65 &7573721072525014933
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7514934556191432061}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &7524229396603156633
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6179630046109234751}
  - component: {fileID: 2163319954207972000}
  - component: {fileID: 1780832356989324976}
  - component: {fileID: 6424514622141965853}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6179630046109234751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7524229396603156633}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 32.8238, y: 0.8778, z: -6.7452}
  m_LocalScale: {x: 1.37226, y: 2.0039616, z: 3.8062623}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2163319954207972000
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7524229396603156633}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1780832356989324976
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7524229396603156633}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &6424514622141965853
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7524229396603156633}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &7683817356631221547
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2626358405268086260}
  - component: {fileID: 1134659734005593765}
  - component: {fileID: 255721361096609409}
  - component: {fileID: 7932234835575805047}
  m_Layer: 0
  m_Name: FakeShadow (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2626358405268086260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7683817356631221547}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.52, y: 0.0001, z: -21.38}
  m_LocalScale: {x: 3.748628, y: 1, z: 1.0968935}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1134659734005593765
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7683817356631221547}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &255721361096609409
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7683817356631221547}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7932234835575805047
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7683817356631221547}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8265368684570234476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4201813904811216181}
  - component: {fileID: 2998486324418378013}
  - component: {fileID: 2477576502727448495}
  m_Layer: 0
  m_Name: Fire
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4201813904811216181
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8265368684570234476}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 1.375, y: 0.397, z: 10.636}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &2998486324418378013
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8265368684570234476}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.5019608, b: 0.098039225, a: 1}
  m_Intensity: 8.29
  m_Range: 3.02
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &2477576502727448495
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8265368684570234476}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &8347439238076348447
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4714503882549410089}
  - component: {fileID: 5231792460133639369}
  - component: {fileID: 6868329448481523724}
  - component: {fileID: 4751089305731061013}
  m_Layer: 0
  m_Name: FakeShadow (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4714503882549410089
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8347439238076348447}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0.70710707, z: -0, w: 0.70710653}
  m_LocalPosition: {x: 6.53, y: 0.0001, z: 5.98}
  m_LocalScale: {x: 1.132136, y: 1, z: 0.7404032}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: -90, z: 0}
--- !u!33 &5231792460133639369
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8347439238076348447}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &6868329448481523724
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8347439238076348447}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4751089305731061013
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8347439238076348447}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8353370155860804107
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2892683757898821926}
  - component: {fileID: 2873584025513551135}
  - component: {fileID: 5717114140774173960}
  - component: {fileID: 1355677262368184849}
  m_Layer: 0
  m_Name: SpawnCollider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2892683757898821926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8353370155860804107}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -4.8, y: 0.35, z: -20.1383}
  m_LocalScale: {x: 0.175, y: 1.1, z: 4.5052214}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2873584025513551135
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8353370155860804107}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5717114140774173960
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8353370155860804107}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &1355677262368184849
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8353370155860804107}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &8393375232372973828
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7596579337683997701}
  - component: {fileID: 7853520414744738991}
  - component: {fileID: 1693615809471498834}
  - component: {fileID: 219173559896534011}
  - component: {fileID: 1526344169142299458}
  m_Layer: 0
  m_Name: '[BuildingBlock] Spatial Audio_Clock'
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7596579337683997701
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393375232372973828}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: -6.84, y: 0, z: -11.66}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7853520414744738991
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393375232372973828}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5b023183069df224080314a560e6e1ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  blockId: 6737262b-4fab-4779-a38b-fe74012fa505
  instanceId: c41c113d-a029-4d95-92ac-cab0e659197d
  version: 1
  installationRoutineCheckpoint:
    _installationRoutineId: 
    _installationVariants: []
--- !u!82 &1693615809471498834
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393375232372973828}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: bb5ec880c877320499b57d66bf2b5b92, type: 3}
  m_Resource: {fileID: 8300000, guid: bb5ec880c877320499b57d66bf2b5b92, type: 3}
  m_PlayOnAwake: 1
  m_Volume: 1
  m_Pitch: 1
  Loop: 1
  Mute: 0
  Spatialize: 1
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 17.0255
  Pan2D: 0
  rolloffMode: 2
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0.25392723
      value: 1.0038605
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.49044427
      value: 0.70328784
      inSlope: -2.0293899
      outSlope: -2.0293899
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.6565169
      value: 0.24028543
      inSlope: -1.9027256
      outSlope: -1.9027256
      tangentMode: 34
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 0.8926697
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &219173559896534011
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393375232372973828}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a07f270ac36c49789466c75ceb88424c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableSpatialization: 1
  gainBoostDb: 0
  enableAcoustics: 1
  reverbSendDb: 0
--- !u!114 &1526344169142299458
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393375232372973828}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 796f04c5a94947649c2235acef0008ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hrtfIntensity: 1
  volumetricRadius: 2.37
  earlyReflectionsSendDb: 0
  reverbReach: 0.5
  occlusionIntensity: 1
  directivityIntensity: 1
  directivityPattern: 0
  directSoundEnabled: 1
  mediumAbsorption: 1
--- !u!1 &8406218548662456446
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 735679482564639504}
  - component: {fileID: 4741304460211853230}
  - component: {fileID: 2139616045716086696}
  - component: {fileID: 4194572825097287501}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &735679482564639504
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8406218548662456446}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 28.8618, y: 0.8778, z: -1.265}
  m_LocalScale: {x: 0.49737576, y: 2.0039616, z: 4.71917}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4741304460211853230
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8406218548662456446}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2139616045716086696
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8406218548662456446}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &4194572825097287501
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8406218548662456446}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &8456440350543949488
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8587699607912629580}
  - component: {fileID: 1760101333284686149}
  - component: {fileID: 5300120561799806281}
  m_Layer: 0
  m_Name: Point Light (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8587699607912629580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8456440350543949488}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 6.364, y: 3.135, z: 2.317}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1760101333284686149
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8456440350543949488}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 4
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &5300120561799806281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8456440350543949488}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &8550410204073113977
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1562648964352834512}
  - component: {fileID: 1418917127052955237}
  - component: {fileID: 1429081318373284658}
  m_Layer: 0
  m_Name: Moon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1562648964352834512
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8550410204073113977}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 53.96, y: 40.6, z: 24.82}
  m_LocalScale: {x: 5.2, y: 5.2, z: 5.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1418917127052955237
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8550410204073113977}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1429081318373284658
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8550410204073113977}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8a50d59e4a6462f47afb9415b9e77764, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8555600150185385165
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3891119415219268799}
  - component: {fileID: 5808747931118751197}
  - component: {fileID: 1615660058171448549}
  - component: {fileID: 8302925052267671879}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3891119415219268799
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8555600150185385165}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 24.7564, y: 0.8778, z: -5.1851}
  m_LocalScale: {x: 1.1798297, y: 2.0039616, z: 0.32435283}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5808747931118751197
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8555600150185385165}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1615660058171448549
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8555600150185385165}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8302925052267671879
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8555600150185385165}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &8609789656016973505
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4696737340708732992}
  - component: {fileID: 854216483118211457}
  - component: {fileID: 2691157458407747160}
  - component: {fileID: 7657484707173313125}
  m_Layer: 0
  m_Name: FakeShadow (11)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4696737340708732992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609789656016973505}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 33.71, y: 0.0001, z: 8.23}
  m_LocalScale: {x: 0.6019414, y: 1, z: 1.0202674}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &854216483118211457
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609789656016973505}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &2691157458407747160
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609789656016973505}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7657484707173313125
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8609789656016973505}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8678479497470349319
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3956831628599067292}
  - component: {fileID: 1376984423469848901}
  - component: {fileID: 7157844522706797119}
  m_Layer: 0
  m_Name: Point Light (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3956831628599067292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8678479497470349319}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 0.273, y: 7.124, z: -15.235}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!108 &1376984423469848901
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8678479497470349319}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 2
  m_Color: {r: 1, g: 0.627451, b: 0.098039225, a: 1}
  m_Intensity: 3
  m_Range: 7.26
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 0
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!114 &7157844522706797119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8678479497470349319}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!1 &8774590263270531284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 30207903662723112}
  - component: {fileID: 8247094627997491076}
  - component: {fileID: 6407586573570634159}
  - component: {fileID: 6892445002528356601}
  m_Layer: 0
  m_Name: FakeShadow (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &30207903662723112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8774590263270531284}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.71, y: 0.0001, z: -7.68}
  m_LocalScale: {x: 2.612084, y: 1, z: 1.453384}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2826503088417032287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &8247094627997491076
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8774590263270531284}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!64 &6407586573570634159
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8774590263270531284}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6892445002528356601
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8774590263270531284}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 33995e3f31b5a6b4597193c9753f73a8, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8903196000184521912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5546768098831521996}
  - component: {fileID: 2981373779528216239}
  - component: {fileID: 1420517310188621429}
  - component: {fileID: 8152165725149221653}
  m_Layer: 12
  m_Name: Spawn_PlayerBlocker
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5546768098831521996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903196000184521912}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 33.6845, y: 1.9975, z: -12.96}
  m_LocalScale: {x: 5.819855, y: 3.995232, z: 0.24188317}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7273606766877193120}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2981373779528216239
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903196000184521912}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1420517310188621429
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903196000184521912}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8a50d59e4a6462f47afb9415b9e77764, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8152165725149221653
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8903196000184521912}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 4096
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 12287
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &9105283673040754544
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 192977920540300741}
  - component: {fileID: 7942056753064061352}
  - component: {fileID: 3341934195333358853}
  - component: {fileID: 8007404450397051593}
  m_Layer: 0
  m_Name: GardenNavMeshCarvers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &192977920540300741
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9105283673040754544}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 24.7564, y: 0.8778, z: 2.687}
  m_LocalScale: {x: 1.1798297, y: 2.0039616, z: 0.32435283}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2066607423450437246}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7942056753064061352
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9105283673040754544}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3341934195333358853
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9105283673040754544}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &8007404450397051593
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9105283673040754544}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1001 &9431867949778646
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 2560565295
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1892
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1697
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (5)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.43
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.0000004768371
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -1.937
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &5754719126867090077 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 9431867949778646}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1119185388348119905
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 492422854
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1756
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1882
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (12)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: 6.46
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.00000031292436
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 34.85
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -360
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &4644752301355173674 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 1119185388348119905}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1664782562065915901
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 4091677754
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1245
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1951
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (11)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.69
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.707107
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071066
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 28.479
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -270
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &6406126785327268790 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 1664782562065915901}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1955875046208549269
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: -8861811765681228779, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -8721234113605021615, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalScale.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalScale.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalScale.z
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalPosition.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8667710598127214084, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -8448198403681316809, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -8332400254159727719, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -8201206159206502212, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -8101027788826285649, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -7993675594469859785, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_CastShadows
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7993675594469859785, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -7807943079572243217, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -7631021064545322588, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -6969435385917763200, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -6932890030751487420, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -6780963010867167714, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -6690283993699474094, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: -6657823512176185530, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: -6531502344059525875, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -6384233765002864941, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -6379294219201363253, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -6195717686590180615, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -6174800815674278062, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5920409083838842560, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5819543744589010143, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5760337616966792483, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -5756462359374269648, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5578005108094350440, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5493437723678710472, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -5438122569575438803, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -5427359056908104413, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -5396931005497341183, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -4869215606779837587, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -4765159745476391986, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -4370164491662426623, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: -4357325900927928067, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_CastShadows
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -4357325900927928067, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -4327669694256676354, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: -4263993267055946371, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -3801036337146939541, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -3419119725345210121, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -3372154799471147523, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -3328325877130214523, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -3153153111300109851, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -3030436917601836353, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -2698671314165209256, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -2647719621681909123, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: -2574567918716962085, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: -2528533968411258172, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -2228472603372156365, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -2076802171262098950, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: -2032277724199071716, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -2003937939316001683, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -1962533265415906583, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: -1961355952160404183, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -1957568700390622958, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: -1909058027033235853, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -1864172678532914657, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: -1564205713175191992, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -1306891651728347425, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -1254336573948831625, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -871013973921046461, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: -551840812256452080, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -489204482515062072, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -368379429472521596, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: -327780854283498171, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -136509361588557619, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -130037491309506653, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -124369472472536895, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 117480929010248096, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 183313680609858991, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: 292897260220545681, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 420789024306589477, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: 515888704323455539, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: 673148357444867428, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 854419102617128678, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 919132149155446097, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_Name
      value: House_Downstairs_Model
      objectReference: {fileID: 0}
    - target: {fileID: 965568537007094682, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 1095889087319334743, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: 1104056042965545881, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 1202038599924982386, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 1259169856994380507, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: 1545567624087400821, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 1584822105481194736, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 1742583129539042803, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 1834444465239548499, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 2155679202691174052, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 2438196217175420294, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 2511811754567536238, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_CastShadows
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2511811754567536238, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 2543111727347718637, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 2585280304480251452, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 2894205726397466003, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 2934984222206370977, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 3139205736551537821, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 3612582832150188905, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 3657044605025578172, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 3659987970807209278, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 3707595053247727776, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 3891576728013805788, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: 3941275204229162585, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 4050050446865405439, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 4070703530485879320, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 4536157381112330850, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 4693078620559246978, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 4799501096897883397, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 5087752497686562715, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 5281763517332586396, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: 5721260379540999523, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 5735409553337643222, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 6081771100210302757, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 6571156393423129025, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 6577325737019909244, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: m_CastShadows
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6577325737019909244, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 6661123630053035156, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 6695916758757630069, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: 6851177928716692621, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 6853658186711984962, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: f3f590a8869a68d4abf2bcfaa1e6bf65, type: 2}
    - target: {fileID: 6856529042309731645, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 345f2a66a341f644ea78b6cf3006ac53, type: 2}
    - target: {fileID: 7104064937156572382, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9d1d5f7bf3b383c4aab5c0f696f29028, type: 2}
    - target: {fileID: 7542169982109088872, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 7944655761460746618, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 2d8fecf90ee06c747a5b9a1558cf7fab, type: 2}
    - target: {fileID: 7999848027258957845, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 8105307659517250260, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 0feb84318b62012428b9c1eeb4768e37, type: 2}
    - target: {fileID: 8159220161903911096, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: 8357582239177517708, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: 8869215451327494105, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 9108794662423252112, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: 9168501063678472370, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4733967648757096331}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4201813904811216181}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8244563822735463083}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8587699607912629580}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6752928572488711752}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3956831628599067292}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1286551046039288994}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5570714371743417333}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8045176283678142228}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7596579337683997701}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1562648964352834512}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5151956065207037864}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8884843353653538816}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4116316361349432036}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7459508192986603096}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 391867044807764223}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7273606766877193120}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8131728787153880412}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3891119415219268799}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6179630046109234751}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 860928143088377144}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 192977920540300741}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 5490324804074966031}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 735679482564639504}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3190545107113384260}
    - targetCorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2826503088417032287}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3054675210969689821, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 9046629226577974723}
    - targetCorrespondingSourceObject: {fileID: 2779567325677866051, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8348302091451475030}
    - targetCorrespondingSourceObject: {fileID: -7563550683289839704, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 501412879229576356}
    - targetCorrespondingSourceObject: {fileID: 6542600274493847892, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2301917841103775273}
    - targetCorrespondingSourceObject: {fileID: 248093231460629515, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2471043215946292164}
    - targetCorrespondingSourceObject: {fileID: -958965512041718797, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 104551969441198022}
    - targetCorrespondingSourceObject: {fileID: 533653803225909672, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8853792033139424179}
    - targetCorrespondingSourceObject: {fileID: 2423692417229934237, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1367783935424961629}
    - targetCorrespondingSourceObject: {fileID: -4336895327313088627, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7790933133745511846}
    - targetCorrespondingSourceObject: {fileID: 977572725826285547, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6740286714749276897}
    - targetCorrespondingSourceObject: {fileID: -8444035908132586996, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 170767009035056440}
    - targetCorrespondingSourceObject: {fileID: -7769145745621662748, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3329254206934773501}
    - targetCorrespondingSourceObject: {fileID: 1324434412065799009, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 889182304674184545}
    - targetCorrespondingSourceObject: {fileID: 1324434412065799009, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6739371189601937551}
    - targetCorrespondingSourceObject: {fileID: -1495678183176934613, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2309149844434374035}
    - targetCorrespondingSourceObject: {fileID: -1574739657103547698, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1668576517508918874}
    - targetCorrespondingSourceObject: {fileID: 7301278612714895962, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4662671788953800327}
    - targetCorrespondingSourceObject: {fileID: -7020467495112429473, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1387132789639325698}
    - targetCorrespondingSourceObject: {fileID: -891823256898513449, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4770548644499598795}
    - targetCorrespondingSourceObject: {fileID: 6055882481800917707, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 375744830003468706}
    - targetCorrespondingSourceObject: {fileID: -1223951080570517160, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1916836103359211617}
    - targetCorrespondingSourceObject: {fileID: 6371058735068206073, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6460102467906106480}
    - targetCorrespondingSourceObject: {fileID: -3006623015308507581, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1395046718376176187}
    - targetCorrespondingSourceObject: {fileID: -6953808364095013155, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3721645021519689139}
    - targetCorrespondingSourceObject: {fileID: 5825515426466537831, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 6970537379050693738}
    - targetCorrespondingSourceObject: {fileID: -5519480980228094843, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 2571422839528881771}
    - targetCorrespondingSourceObject: {fileID: -8499293836784428481, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3445788936826336871}
    - targetCorrespondingSourceObject: {fileID: 6255401494728082989, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7560640320387598253}
    - targetCorrespondingSourceObject: {fileID: -6995604278993192597, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7241804210588018254}
  m_SourcePrefab: {fileID: 100100000, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
--- !u!1 &314044569886281544 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -6953808364095013155, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &3721645021519689139
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 314044569886281544}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.3469386, y: 1.5183629, z: 1.3401812}
  m_Center: {x: 0.000000059604645, y: 0.75918144, z: 0}
--- !u!1 &411767103375933898 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -7020467495112429473, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &1387132789639325698
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 411767103375933898}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.889333, y: 2.7889988, z: 1.8429121}
  m_Center: {x: -1.4446665, y: 1.3944994, z: 0.92145604}
--- !u!1 &418332310327773438 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -6995604278993192597, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &7241804210588018254
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 418332310327773438}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.1383624, y: 2.229093, z: 3.6700692}
  m_Center: {x: 0.00000005960466, y: -0.000000059604645, z: -1.1832914e-30}
--- !u!1 &668221497154964212 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1324434412065799009, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!64 &889182304674184545
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 668221497154964212}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 5287530492178025666, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
--- !u!114 &6739371189601937551
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 668221497154964212}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a5ac11cc976e418e8d13136b07e1f52, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SerializedVersion: 0
  m_AgentTypeID: 0
  m_CollectObjects: 0
  m_Size: {x: 10, y: 10, z: 10}
  m_Center: {x: 0, y: 2, z: 0}
  m_LayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_UseGeometry: 1
  m_DefaultArea: 0
  m_GenerateLinks: 0
  m_IgnoreNavMeshAgent: 1
  m_IgnoreNavMeshObstacle: 1
  m_OverrideTileSize: 0
  m_TileSize: 256
  m_OverrideVoxelSize: 0
  m_VoxelSize: 0.16666667
  m_MinRegionArea: 2
  m_NavMeshData: {fileID: 23800000, guid: cc9eae248eb5157498da0af42ad22568, type: 2}
  m_BuildHeightMesh: 0
--- !u!1 &877207178343556669 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -7563550683289839704, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &501412879229576356
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 877207178343556669}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158397, y: 3.2475643, z: 1.4794294}
  m_Center: {x: -1.3431611e-23, y: 1.6237822, z: 0.00082579267}
--- !u!1 &1083919344090772081 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -7769145745621662748, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!64 &3329254206934773501
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1083919344090772081}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 3070608106298808885, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
--- !u!1 &1236476367642799018 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -8499293836784428481, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &3445788936826336871
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1236476367642799018}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.6965146, y: 3.3939495, z: 5.6815233}
  m_Center: {x: 0.13473535, y: -0.033919573, z: -0.060405374}
--- !u!1 &1293777643905387417 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -8444035908132586996, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &170767009035056440
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1293777643905387417}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.88, y: 8.53, z: 1.8384789}
  m_Center: {x: 0.0024840832, y: 4.265, z: 0.2699036}
--- !u!1 &1636390833671626366 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 977572725826285547, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &6740286714749276897
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1636390833671626366}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.3616776, y: 6.6358476, z: 1.5540718}
  m_Center: {x: 0, y: 3.3179238, z: 0}
--- !u!1 &1753534709921222046 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 248093231460629515, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &2471043215946292164
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1753534709921222046}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158395, y: 3.2475643, z: 1.4794292}
  m_Center: {x: 0, y: 1.6237822, z: 0.00082579255}
--- !u!1 &2036544795553216573 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 533653803225909672, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &8853792033139424179
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2036544795553216573}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158393, y: 3.2475643, z: 1.4794291}
  m_Center: {x: 0, y: 1.6237822, z: 0.0008257925}
--- !u!4 &2066607423450437246 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2901009440762590480 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -5519480980228094843, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &2571422839528881771
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2901009440762590480}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.9554272, y: 3.292, z: 1.2808576}
  m_Center: {x: 0, y: 1.646, z: 0.13660726}
--- !u!1 &3549059803534171976 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3054675210969689821, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &9046629226577974723
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3549059803534171976}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.3848913, y: 2.017802, z: 1.1294922}
  m_Center: {x: 0, y: 1.008901, z: 0}
--- !u!1 &4217065335674896136 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2423692417229934237, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &1367783935424961629
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4217065335674896136}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158397, y: 3.2475643, z: 1.4794294}
  m_Center: {x: -2.6863222e-24, y: 1.6237822, z: 0.00082579267}
--- !u!1 &4447208134548684246 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2779567325677866051, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &8348302091451475030
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4447208134548684246}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.3848912, y: 2.017802, z: 1.129492}
  m_Center: {x: -0.69244546, y: -1.008901, z: -0.4787532}
--- !u!1 &4751110367757437121 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6542600274493847892, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &2301917841103775273
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4751110367757437121}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158395, y: 3.2475643, z: 1.4794292}
  m_Center: {x: 5.3726445e-24, y: 1.6237822, z: 0.00082579255}
--- !u!1 &4849855457842193004 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6371058735068206073, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &6460102467906106480
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4849855457842193004}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 5.880001, y: 3.9185243, z: 1.4695712}
  m_Center: {x: -2.9400005, y: -0.8297367, z: 0.73478574}
--- !u!1 &5475473999923712242 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5825515426466537831, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &6970537379050693738
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5475473999923712242}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.3469386, y: 1.5183629, z: 1.3401812}
  m_Center: {x: 0.000000059604645, y: 0.75918144, z: 0}
--- !u!1 &5576298229404432342 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -3006623015308507581, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &1395046718376176187
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5576298229404432342}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.9166057, y: 3.292, z: 3.5879233}
  m_Center: {x: 0, y: 1.646, z: 0}
--- !u!1 &5614598376016885688 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6255401494728082989, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &7560640320387598253
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5614598376016885688}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 8.215007, y: 3.3939495, z: 2.7182298}
  m_Center: {x: -0.00032663345, y: -0.033919573, z: -0.12387782}
--- !u!1 &5705630774994043742 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6055882481800917707, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &375744830003468706
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5705630774994043742}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.9600003, y: 2.7889988, z: 1.2737055}
  m_Center: {x: -1.9800001, y: -1.3944994, z: -0.63616943}
--- !u!1 &6410004701898350104 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -4336895327313088627, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &7790933133745511846
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6410004701898350104}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 6.2949543, y: 2.495289, z: 3.49071}
  m_Center: {x: 0.004564166, y: 1.2476445, z: 0}
--- !u!1 &7546681147274454082 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -891823256898513449, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4770548644499598795
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7546681147274454082}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.98, y: 2.7889988, z: 2.0800002}
  m_Center: {x: -0.02111268, y: -1.3944994, z: -1.0400006}
--- !u!1 &7608189522167769702 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -958965512041718797, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &104551969441198022
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7608189522167769702}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.5158395, y: 3.2475643, z: 1.4794292}
  m_Center: {x: 0, y: 1.6237822, z: 0.00082579255}
--- !u!1 &8078016684127967934 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1495678183176934613, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &2309149844434374035
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8078016684127967934}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 3.5408196, y: 2.9165046, z: 2.5990155}
  m_Center: {x: -0.43979716, y: 1.4582521, z: 0.041190267}
--- !u!1 &8143017603473920859 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1574739657103547698, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &1668576517508918874
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8143017603473920859}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 2.5656018, y: 2.9165046, z: 3.438169}
  m_Center: {x: 0.047811747, y: 1.4582521, z: -0.37838528}
--- !u!1 &8369674959364831437 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -1223951080570517160, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &1916836103359211617
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8369674959364831437}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 6.080679, y: 2.7889988, z: 2.6246996}
  m_Center: {x: -0.00000023841858, y: 1.3944994, z: 0.6754968}
--- !u!1 &9113020424467811279 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7301278612714895962, guid: 111bb5c5209163d44b9eb42b643aaa09, type: 3}
  m_PrefabInstance: {fileID: 1955875046208549269}
  m_PrefabAsset: {fileID: 0}
--- !u!65 &4662671788953800327
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9113020424467811279}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.1106666, y: 2.7889988, z: 1.2737054}
  m_Center: {x: 0.5553333, y: 1.3944994, z: 0.6368527}
--- !u!1001 &2688588491512416778
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 1496979696
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1474.0011
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1474.0001
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.51983243
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.8542683
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -117.358
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (4)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -15.51
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -2.72
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -270
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &7688370556310459969 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 2688588491512416778}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3244141152163452151
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 3447402054
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1536
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1697
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710695
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1.81
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: -8.21
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &7131693574153160892 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 3244141152163452151}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3475205299331261036
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 3514422875
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2865.004
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -913.00104
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.9063077
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.42261845
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 310
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (14)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -4.523
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.42261827
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.9063079
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 15.776
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -130
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &9207385666429255207 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 3475205299331261036}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3579348837960901357
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 1722322223
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 2449
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -178
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (1)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: 7.255
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 8.018
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &9102120610915810982 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 3579348837960901357}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3584047915373981771
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 2748490617
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1838
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2060
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (9)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -12.97
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.0000006407499
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 22.48
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -180
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &9097346163268414464 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 3584047915373981771}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4104200530781608004
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7273606766877193120}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalPosition.x
      value: -3.948101
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalPosition.z
      value: -19.747
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -7511558181221131132, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: 'm_Materials.Array.data[1]'
      value: 
      objectReference: {fileID: 2100000, guid: abc47b39da475cd4e8af79235b4cd41f, type: 2}
    - target: {fileID: 919132149155446097, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
      propertyPath: m_Name
      value: FridgeSpawn
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
--- !u!4 &4575575712501104559 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: bae43dcea730fcf4c8f8fad90e994880, type: 3}
  m_PrefabInstance: {fileID: 4104200530781608004}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4399327312970125254
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7273606766877193120}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalPosition.x
      value: 34.37
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.012864828
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalPosition.z
      value: 8.49
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 3767b2ed85772164584b8b96016e259f, type: 2}
    - target: {fileID: -7511558181221131132, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: 'm_Materials.Array.data[1]'
      value: 
      objectReference: {fileID: 2100000, guid: abc47b39da475cd4e8af79235b4cd41f, type: 2}
    - target: {fileID: 919132149155446097, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
      propertyPath: m_Name
      value: GardenSpawn
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
--- !u!4 &4217398522653141037 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: c2dd20c623209634d8c79afef0169dee, type: 3}
  m_PrefabInstance: {fileID: 4399327312970125254}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4771912754708743042
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7273606766877193120}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: d396fa3387e66be42807eda2c31a912c, type: 2}
    - target: {fileID: -7511558181221131132, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: 'm_Materials.Array.data[1]'
      value: 
      objectReference: {fileID: 2100000, guid: abc47b39da475cd4e8af79235b4cd41f, type: 2}
    - target: {fileID: 919132149155446097, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
      propertyPath: m_Name
      value: WallSpawn
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
--- !u!4 &5022503296870343785 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: e0f6ebd718c113949953a14c6d93b78e, type: 3}
  m_PrefabInstance: {fileID: 4771912754708743042}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4824852614131652289
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 745018996
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1385
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1697
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (6)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -9.8
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.0000004768371
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 1.73
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &939015185716644490 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 4824852614131652289}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5475143619078782397
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 1682973414
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 943.0002
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1886.9999
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071065
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071071
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (3)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -14.793
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071064
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071072
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 6.868
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -270
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &290133258540671478 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 5475143619078782397}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5501834042199629594
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 3672804905
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1795
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (2)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -0.469
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 3.648
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0.55999994
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &263227880829207377 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 5501834042199629594}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5805738859104012871
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 3721898392
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3097578982772561148, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_PresetInfoIsWorld
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1827
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1951
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (13)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.37
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.707107
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071066
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 28.48
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -270
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &2265165540553889292 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 5805738859104012871}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6011398892586728238
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 3567813607
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1627
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1632
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (10)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -2.9
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.7071067
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071069
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 36.488
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -270
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &2058597774331930469 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 6011398892586728238}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &6106570887542355993
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 1089143853
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -1385
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1925
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (7)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -12.501
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.0000005066395
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 9.96
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &1964617735871407186 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 6106570887542355993}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7597295577563563183
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7273606766877193120}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -7511558181221131132, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 5bf333b9768c3aa4794278d7cbd60695, type: 2}
    - target: {fileID: -7511558181221131132, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: 'm_Materials.Array.data[1]'
      value: 
      objectReference: {fileID: 2100000, guid: abc47b39da475cd4e8af79235b4cd41f, type: 2}
    - target: {fileID: 919132149155446097, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
      propertyPath: m_Name
      value: FenceSpawn
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
--- !u!4 &7991001394602566468 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: f58e7409df1fcc74f9b43f31cd066ae2, type: 3}
  m_PrefabInstance: {fileID: 7597295577563563183}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8435575104629195292
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2100548841853415128, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 1635234147
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 3097578982772561148, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_PresetInfoIsWorld
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1869
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -1841
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.70710707
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710653
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 270
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (15)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.37
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.70710665
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.7071069
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 29.17
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8830772412066695759, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &4245894342024453719 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 8435575104629195292}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8889656154918940655
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 391867044807764223}
    m_Modifications:
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: SortKey
      value: 2703973553
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: NestedObjects.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2981303202149843442, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: 'NestedObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: -2528
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: -1561.5005
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: -342
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4751490484260739074, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Name
      value: TestingDefence&EnemySpawner (8)
      objectReference: {fileID: 0}
    - target: {fileID: 5183064283176963362, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.x
      value: 1920
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_SizeDelta.y
      value: 1080
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalPosition.z
      value: 5.9539986
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 12.191
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 1.56
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5968429186659629012, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
--- !u!224 &3792940842997969828 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 5763865033678789707, guid: f33894ab6c29a594b9b5eb32d0912776, type: 3}
  m_PrefabInstance: {fileID: 8889656154918940655}
  m_PrefabAsset: {fileID: 0}
