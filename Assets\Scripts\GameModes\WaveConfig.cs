using UnityEngine;
using System;
using System.Collections.Generic;
using Fusion;


/// <summary>
/// Scriptable object for configuring wave-based game modes.
/// Allows for detailed customization of enemy waves.
/// </summary>
[CreateAssetMenu(fileName = "WaveConfig", menuName = "Game/Wave Configuration")]
public class WaveConfig : ScriptableObject
{
    [Serializable]
    public class EnemySpawnInfo
    {
        public string enemyName;
        public NetworkPrefabRef enemyPrefab;
        public float spawnWeight = 1f; // Higher weight = more likely to spawn
        [Range(0f, 1f)]
        public float difficultyScaling = 0.1f; // How much this enemy's spawn chance increases with difficulty
    }

    [Serializable]
    public class WaveInfo
    {
        public string waveName;
        public int baseEnemyCount = 10;
        [Range(0.1f, 5f)]
        public float enemyCountMultiplier = 1f; // Multiplier for the base enemy count
        public float timeBetweenSpawns = 1f;
        public List<EnemySpawnInfo> enemies = new();
        public float cooldownAfterWave = 20f;
        [Range(0f, 1f)]
        public float difficultyIncrease = 0.1f; // How much the difficulty increases after this wave
    }

    [Header("Wave Settings")]
    public List<WaveInfo> waves = new();

    [Header("Game Mode Settings")]
    public bool isEndless = false;
    public int endlessStartWave = 10; // Wave to start repeating from in endless mode
    [Range(0.5f, 2f)]
    public float globalDifficultyMultiplier = 1f; // Global multiplier for all difficulty settings

    // Helper method to get wave info
    public WaveInfo GetWaveInfo(int waveIndex)
    {
        // Ensure we have waves defined
        if (waves == null || waves.Count == 0)
        {
            Debug.LogWarning("No waves defined in WaveConfig");
            return null;
        }

        // Ensure wave index is valid
        if (waveIndex < 0)
        {
            Debug.LogWarning($"Invalid wave index: {waveIndex}");
            return waves[0]; // Return first wave as fallback
        }

        // Normal case - wave index is within range
        if (waveIndex < waves.Count)
        {
            return waves[waveIndex];
        }
        // Endless mode - cycle through waves
        else if (isEndless && waves.Count > 0)
        {
            // Make sure endlessStartWave is valid
            int validEndlessStartWave = Mathf.Clamp(endlessStartWave, 0, waves.Count - 1);

            // For endless mode, cycle through waves starting from endlessStartWave
            if (validEndlessStartWave < waves.Count - 1)
            {
                // We have multiple waves to cycle through
                int cycleRange = waves.Count - validEndlessStartWave;
                int cycleOffset = (waveIndex - waves.Count) % cycleRange;
                int cycleIndex = validEndlessStartWave + cycleOffset;

                return waves[cycleIndex];
            }
            else
            {
                // Just repeat the last wave
                return waves[^1];
            }
        }

        // Fallback to the last wave if we're out of range and not in endless mode
        Debug.LogWarning($"Wave index {waveIndex} out of range, using last wave");
        return waves[^1];
    }

    // Helper method to calculate enemy count for a wave based on difficulty
    public int CalculateEnemyCount(int waveIndex, float difficultyMultiplier)
    {
        try
        {
            WaveInfo waveInfo = GetWaveInfo(waveIndex);
            if (waveInfo == null)
            {
                Debug.LogWarning($"No wave info found for wave {waveIndex}");
                return 1; // Default to 1 enemy
            }

            // Base count multiplied by wave's multiplier and global difficulty
            float baseCount = waveInfo.baseEnemyCount * waveInfo.enemyCountMultiplier;

            // Apply additional scaling based on wave number and difficulty
            float waveScaling = 1f + (Mathf.Max(0, waveIndex) * 0.1f); // 10% increase per wave

            // Calculate final count
            int finalCount = Mathf.RoundToInt(baseCount * waveScaling * Mathf.Max(0.1f, difficultyMultiplier) * Mathf.Max(0.1f, globalDifficultyMultiplier));

            return Mathf.Max(1, finalCount); // Ensure at least 1 enemy
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error calculating enemy count: {ex.Message}");
            return 1; // Default to 1 enemy on error
        }
    }

    // Helper method to select an enemy prefab based on weights
    public NetworkPrefabRef SelectEnemyPrefab(int waveIndex, float difficultyMultiplier)
    {
        try
        {
            WaveInfo waveInfo = GetWaveInfo(waveIndex);
            if (waveInfo == null)
            {
                Debug.LogWarning($"No wave info found for wave {waveIndex}");
                return default;
            }

            if (waveInfo.enemies == null || waveInfo.enemies.Count == 0)
            {
                Debug.LogWarning($"No enemies defined for wave {waveIndex}");
                return default;
            }

            // Calculate total weight
            float totalWeight = 0f;
            foreach (var enemy in waveInfo.enemies)
            {
                // Apply difficulty scaling to weights
                float scaledWeight = enemy.spawnWeight * (1f + (enemy.difficultyScaling * difficultyMultiplier * Mathf.Max(0, waveIndex)));
                totalWeight += scaledWeight;
            }

            // If total weight is zero, just return the first enemy
            if (totalWeight <= 0f)
            {
                Debug.LogWarning($"Total enemy weight is zero for wave {waveIndex}");
                return waveInfo.enemies[0].enemyPrefab;
            }

            // Select an enemy based on weighted probability
            float randomValue = UnityEngine.Random.Range(0f, totalWeight);
            float currentWeight = 0f;

            foreach (var enemy in waveInfo.enemies)
            {
                float scaledWeight = enemy.spawnWeight * (1f + (enemy.difficultyScaling * difficultyMultiplier * Mathf.Max(0, waveIndex)));
                currentWeight += scaledWeight;

                if (randomValue <= currentWeight)
                    return enemy.enemyPrefab;
            }

            // Fallback to first enemy
            return waveInfo.enemies[0].enemyPrefab;
        }
        catch (Exception ex)
        {
            Debug.LogError($"Error selecting enemy prefab: {ex.Message}");
            return default;
        }
    }
}
