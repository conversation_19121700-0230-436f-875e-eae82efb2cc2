fileFormatVersion: 2
guid: e725a070cec140c4caffb81624c8c787
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder:
    Fusion.HitboxRoot: -2000
    Fusion.NetworkMecanimAnimator: 600
    Fusion.NetworkObject: 5000
    Fusion.NetworkTransform: -1400
    Fusion.NetworkObjectInactivityGuard: -10000
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 1
  platformData:
  - first:
      Any: 
    second:
      enabled: 1
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 0
      settings:
        DefaultValueInitialized: true
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 267958
  packageName: Photon Fusion
  packageVersion: 2.0.6
  assetPath: Assets/Photon/Fusion/Assemblies/Fusion.Runtime.dll
  uploadId: 755233
