The target system is: Android - 31 - aarch64
The host system is: Windows - 10.0.26100 - AMD64
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/clang.exe 
Build flags: 
Id flags: -c;--target=aarch64-none-linux-android31 

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

The C compiler identification is Clang, found in "C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/bin/clang++.exe 
Build flags: 
Id flags: -c;--target=aarch64-none-linux-android31 

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

The CXX compiler identification is Clang, found in "C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/3.22.1-g37088a8-dirty/CompilerIdCXX/CMakeCXXCompilerId.o"

Detecting C compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja.exe cmTC_6b352 && [1/2] Building C object CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o

Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android31

Thread model: posix

InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin

Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

 (in-process)

 "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android31 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8" -dependency-file "CMakeFiles\\cmTC_6b352.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include" -internal-isystem "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include" -O2 -Wformat -fdebug-compilation-dir "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -x c "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c"

clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-w64-windows-gnu

ignoring nonexistent directory "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include

 C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\lib64\clang\12.0.8\include

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking C executable cmTC_6b352

Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android31

Thread model: posix

InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin

Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

 "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld" "--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_6b352 "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib" --build-id=sha1 --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -latomic -lm "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o"




Parsed C implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
    add: [C:/PROGRA~1/Unity/Hub/Editor/60000~1.37F/Editor/Data/PLAYBA~1/ANDROI~1/NDK/TOOLCH~1/llvm/prebuilt/WINDOW~1/lib64/clang/12.0.8/include]
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  collapse include dir [C:/PROGRA~1/Unity/Hub/Editor/60000~1.37F/Editor/Data/PLAYBA~1/ANDROI~1/NDK/TOOLCH~1/llvm/prebuilt/WINDOW~1/lib64/clang/12.0.8/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/include]
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/include;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed C implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja.exe cmTC_6b352 && [1/2] Building C object CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android31]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin]
  ignore line: [Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\clang.exe" -cc1 -triple aarch64-none-linux-android31 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCCompilerABI.c -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8" -dependency-file "CMakeFiles\\cmTC_6b352.dir\\CMakeCCompilerABI.c.o.d" -MT CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include" -internal-isystem "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include" -O2 -Wformat -fdebug-compilation-dir "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -x c "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c"]
  ignore line: [clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  ignore line: [ C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\lib64\clang\12.0.8\include]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking C executable cmTC_6b352]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android31]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin]
  ignore line: [Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld" "--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_6b352 "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib" --build-id=sha1 --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o -latomic -lm "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o"]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld] ==> ignore
    arg [--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_6b352] ==> ignore
    arg [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o] ==> obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o]
    arg [-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64] ==> dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64]
    arg [-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_6b352.dir/CMakeCCompilerABI.c.o] ==> ignore
    arg [-latomic] ==> lib [atomic]
    arg [-lm] ==> lib [m]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o] ==> obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o]
  remove lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  remove lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  collapse obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtbegin_dynamic.o]
  collapse obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtend_android.o]
  collapse library dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
  collapse library dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [atomic;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtbegin_dynamic.o;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtend_android.o]
  implicit dirs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/lib/linux/aarch64;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


Detecting CXX compiler ABI info compiled with the following output:
Change Dir: C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/CMakeTmp

Run Build Command(s):C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja.exe cmTC_6ef0a && [1/2] Building CXX object CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o

Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android31

Thread model: posix

InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin

Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

 (in-process)

 "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android31 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8" -dependency-file "CMakeFiles\\cmTC_6ef0a.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include" -internal-isystem "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include" -O2 -Wformat -fdeprecated-macro -fdebug-compilation-dir "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -x c++ "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp"

clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-w64-windows-gnu

ignoring nonexistent directory "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"

#include "..." search starts here:

#include <...> search starts here:

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include

 C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\lib64\clang\12.0.8\include

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android

 C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include

End of search list.

[2/2] Linking CXX executable cmTC_6ef0a

Android (7714059, based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)

Target: aarch64-none-linux-android31

Thread model: posix

InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin

Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x

Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x

Candidate multilib: .;@m64

Selected multilib: .;@m64

 "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld" "--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_6ef0a "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib" --build-id=sha1 --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -latomic -lm -lc++ -lm "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o"




Parsed CXX implicit include dir info from above output: rv=done
  found start of include info
  found start of implicit include info
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
    add: [C:/PROGRA~1/Unity/Hub/Editor/60000~1.37F/Editor/Data/PLAYBA~1/ANDROI~1/NDK/TOOLCH~1/llvm/prebuilt/WINDOW~1/lib64/clang/12.0.8/include]
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
    add: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  end of search list found
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  collapse include dir [C:/PROGRA~1/Unity/Hub/Editor/60000~1.37F/Editor/Data/PLAYBA~1/ANDROI~1/NDK/TOOLCH~1/llvm/prebuilt/WINDOW~1/lib64/clang/12.0.8/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/include]
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  collapse include dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  implicit include dirs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/include;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]


Parsed CXX implicit link information from above output:
  link line regex: [^( *|.*[/\])(ld\.lld\.exe|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\]+-)?ld|collect2)[^/\]*( |$)]
  ignore line: [Change Dir: C:/Users/<USER>/Documents/WavingBearStudio/StuffedVR/StuffedVR_Unity/.utmp/RelWithDebInfo/61t5b1g1/arm64-v8a/CMakeFiles/CMakeTmp]
  ignore line: []
  ignore line: [Run Build Command(s):C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/bin/ninja.exe cmTC_6ef0a && [1/2] Building CXX object CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android31]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin]
  ignore line: [Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  ignore line: [ (in-process)]
  ignore line: [ "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\clang++.exe" -cc1 -triple aarch64-none-linux-android31 -emit-obj --mrelax-relocations -mnoexecstack -disable-free -disable-llvm-verifier -discard-value-names -main-file-name CMakeCXXCompilerABI.cpp -mrelocation-model pic -pic-level 2 -pic-is-pie -mframe-pointer=non-leaf -fno-rounding-math -mconstructor-aliases -munwind-tables -target-cpu generic -target-feature +neon -target-abi aapcs -mllvm -aarch64-fix-cortex-a53-835769=1 -fallow-half-arguments-and-returns -fno-split-dwarf-inlining -debug-info-kind=limited -dwarf-version=4 -debugger-tuning=gdb -v -ffunction-sections -fdata-sections -resource-dir "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8" -dependency-file "CMakeFiles\\cmTC_6ef0a.dir\\CMakeCXXCompilerABI.cpp.o.d" -MT CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -sys-header-deps -D ANDROID -D _FORTIFY_SOURCE=2 -D NDEBUG -isysroot "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1" -internal-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include" -internal-isystem "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include" -internal-externc-isystem "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include" -O2 -Wformat -fdeprecated-macro -fdebug-compilation-dir "C:\\Users\\<USER>\\Documents\\WavingBearStudio\\StuffedVR\\StuffedVR_Unity\\.utmp\\RelWithDebInfo\\61t5b1g1\\arm64-v8a\\CMakeFiles\\CMakeTmp" -ferror-limit 19 -stack-protector 2 -fno-signed-char -fgnuc-version=4.2.1 -fcxx-exceptions -fexceptions -vectorize-loops -vectorize-slp -o CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -x c++ "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/SDK/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp"]
  ignore line: [clang -cc1 version 12.0.8 based upon LLVM 12.0.8git default target x86_64-w64-windows-gnu]
  ignore line: [ignoring nonexistent directory "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/include"]
  ignore line: [#include "..." search starts here:]
  ignore line: [#include <...> search starts here:]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/c++/v1]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/local/include]
  ignore line: [ C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\lib64\clang\12.0.8\include]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include/aarch64-linux-android]
  ignore line: [ C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/include]
  ignore line: [End of search list.]
  ignore line: [[2/2] Linking CXX executable cmTC_6ef0a]
  ignore line: [Android (7714059  based on r416183c1) clang version 12.0.8 (https://android.googlesource.com/toolchain/llvm-project c935d99d7cf2016289302412d708641d52d2f7ee)]
  ignore line: [Target: aarch64-none-linux-android31]
  ignore line: [Thread model: posix]
  ignore line: [InstalledDir: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin]
  ignore line: [Found candidate GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android\4.9.x]
  ignore line: [Selected GCC installation: C:\PROGRA~1\Unity\Hub\Editor\60000~1.37F\Editor\Data\PLAYBA~1\ANDROI~1\NDK\TOOLCH~1\llvm\prebuilt\WINDOW~1\bin/../lib/gcc/aarch64-linux-android/4.9.x]
  ignore line: [Candidate multilib: .]
  ignore line: [@m64]
  ignore line: [Selected multilib: .]
  ignore line: [@m64]
  link line: [ "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld" "--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot" -pie -z noexecstack -EL --fix-cortex-a53-843419 --warn-shared-textrel -z now -z relro -z max-page-size=4096 --hash-style=gnu --enable-new-dtags --eh-frame-hdr -m aarch64linux -dynamic-linker /system/bin/linker64 -o cmTC_6ef0a "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64" "-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android" "-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib" --build-id=sha1 --fatal-warnings --no-undefined --gc-sections CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o -latomic -lm -lc++ -lm "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl -lc "C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a" -l:libunwind.a -ldl "C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o"]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\ld] ==> ignore
    arg [--sysroot=C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot] ==> ignore
    arg [-pie] ==> ignore
    arg [-znoexecstack] ==> ignore
    arg [-EL] ==> ignore
    arg [--fix-cortex-a53-843419] ==> ignore
    arg [--warn-shared-textrel] ==> ignore
    arg [-znow] ==> ignore
    arg [-zrelro] ==> ignore
    arg [-zmax-page-size=4096] ==> ignore
    arg [--hash-style=gnu] ==> ignore
    arg [--enable-new-dtags] ==> ignore
    arg [--eh-frame-hdr] ==> ignore
    arg [-m] ==> ignore
    arg [aarch64linux] ==> ignore
    arg [-dynamic-linker] ==> ignore
    arg [/system/bin/linker64] ==> ignore
    arg [-o] ==> ignore
    arg [cmTC_6ef0a] ==> ignore
    arg [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o] ==> obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o]
    arg [-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64] ==> dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64]
    arg [-LC:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
    arg [-LC:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
    arg [--build-id=sha1] ==> ignore
    arg [--fatal-warnings] ==> ignore
    arg [--no-undefined] ==> ignore
    arg [--gc-sections] ==> ignore
    arg [CMakeFiles/cmTC_6ef0a.dir/CMakeCXXCompilerABI.cpp.o] ==> ignore
    arg [-latomic] ==> lib [atomic]
    arg [-lm] ==> lib [m]
    arg [-lc++] ==> lib [c++]
    arg [-lm] ==> lib [m]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [-lc] ==> lib [c]
    arg [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a] ==> lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
    arg [-l:libunwind.a] ==> lib [-l:libunwind.a]
    arg [-ldl] ==> lib [dl]
    arg [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o] ==> obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o]
  remove lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  remove lib [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\libclang_rt.builtins-aarch64-android.a]
  collapse obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtbegin_dynamic.o] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtbegin_dynamic.o]
  collapse obj [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31\\crtend_android.o] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtend_android.o]
  collapse library dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\lib64\\clang\\12.0.8\\lib\\linux\\aarch64] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/lib/linux/aarch64]
  collapse library dir [C:\\PROGRA~1\\Unity\\Hub\\Editor\\60000~1.37F\\Editor\\Data\\PLAYBA~1\\ANDROI~1\\NDK\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin/../lib/gcc/aarch64-linux-android/4.9.x] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android]
  collapse library dir [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib] ==> [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit libs: [atomic;m;c++;m;-l:libunwind.a;dl;c;-l:libunwind.a;dl]
  implicit objs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtbegin_dynamic.o;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31/crtend_android.o]
  implicit dirs: [C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib64/clang/12.0.8/lib/linux/aarch64;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/lib/gcc/aarch64-linux-android/4.9.x;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android/31;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib/aarch64-linux-android;C:/Program Files/Unity/Hub/Editor/6000.0.37f1/Editor/Data/PlaybackEngines/AndroidPlayer/NDK/toolchains/llvm/prebuilt/windows-x86_64/sysroot/usr/lib]
  implicit fwks: []


