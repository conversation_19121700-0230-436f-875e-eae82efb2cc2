                        -HC:\Users\<USER>\Documents\WavingBearStudio\StuffedVR\StuffedVR_Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\src\main\cpp
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=31
-DANDROID_PLATFORM=android-31
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=C:\Program Files\Unity\Hub\Editor\6000.0.37f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
-DCMAKE_ANDROID_NDK=C:\Program Files\Unity\Hub\Editor\6000.0.37f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK
-DCMAKE_TOOLCHAIN_FILE=C:\Program Files\Unity\Hub\Editor\6000.0.37f1\Editor\Data\PlaybackEngines\AndroidPlayer\NDK\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Program Files\Unity\Hub\Editor\6000.0.37f1\Editor\Data\PlaybackEngines\AndroidPlayer\SDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\Documents\WavingBearStudio\StuffedVR\StuffedVR_Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\RelWithDebInfo\61t5b1g1\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\Documents\WavingBearStudio\StuffedVR\StuffedVR_Unity\Library\Bee\Android\Prj\IL2CPP\Gradle\unityLibrary\build\intermediates\cxx\RelWithDebInfo\61t5b1g1\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-DCMAKE_FIND_ROOT_PATH=C:\Users\<USER>\Documents\WavingBearStudio\StuffedVR\StuffedVR_Unity\.utmp\RelWithDebInfo\61t5b1g1\prefab\arm64-v8a\prefab
-BC:\Users\<USER>\Documents\WavingBearStudio\StuffedVR\StuffedVR_Unity\.utmp\RelWithDebInfo\61t5b1g1\arm64-v8a
-GNinja
-DANDROID_STL=c++_shared
                        Build command args: []
                        Version: 2