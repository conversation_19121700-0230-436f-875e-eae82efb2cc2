using UnityEngine;
using Fusion;
using System.Collections.Generic;
using Projectiles;

/// <summary>
/// Add this component to a game scene to set up the game mode.
/// This component should be placed on a GameObject in the scene.
/// </summary>
public class GameModeSetup : MonoBehaviour
{
    [Header("Game Mode Settings")]
    [SerializeField] private GameModeManager gameModeManagerPrefab;

    [<PERSON><PERSON>("Game Modes")]
    [SerializeField] private GameModeBase hordeModePrefab;
    [SerializeField] private GameModeBase teamModePrefab;

    [Header("Wave Configuration")]
    [SerializeField] private WaveConfig defaultHordeConfig;
    [SerializeField] private WaveConfig defaultTeamConfig;

    private void Awake()
    {
        // This component is only needed during scene setup
        // It can be disabled after initialization
        enabled = false;
    }

    /// <summary>
    /// Called by the NetworkRunner when the scene is loaded.
    /// This method is called by the GameManager.
    /// </summary>
    public void SetupGameMode(NetworkRunner runner)
    {
        if (runner == null || !runner.IsServer)
            return;

        Debug.Log("Setting up game mode");

        // Check if a GameModeManager already exists
        var existingManager = runner.SimulationUnityScene.GetComponent<GameModeManager>(true);
        if (existingManager != null)
        {
            Debug.Log("GameModeManager already exists in scene");
            return;
        }

        // Spawn the GameModeManager
        if (gameModeManagerPrefab != null)
        {
            var gameModeManager = runner.Spawn(gameModeManagerPrefab);
            if (gameModeManager != null)
            {
                Debug.Log("GameModeManager spawned successfully");

                // Add game modes to the manager
                AddGameModesToManager(gameModeManager.GetComponent<GameModeManager>());
            }
            else
            {
                Debug.LogError("Failed to spawn GameModeManager");
            }
        }
        else
        {
            Debug.LogError("GameModeManager prefab not assigned");
        }
    }

    private void AddGameModesToManager(GameModeManager manager)
    {
        if (manager == null)
            return;

        // Add game modes to the manager
        if (hordeModePrefab != null && teamModePrefab != null)
        {
            // Create game mode info for Horde Mode
            var hordeInfo = new GameModeManager.GameModeInfo
            {
                modeName = "Horde Mode",
                modePrefab = hordeModePrefab,
                defaultWaveConfig = defaultHordeConfig
            };

            // Create game mode info for Team Mode
            var teamInfo = new GameModeManager.GameModeInfo
            {
                modeName = "Team Mode",
                modePrefab = teamModePrefab,
                defaultWaveConfig = defaultTeamConfig
            };

            // Add game modes to the manager
            manager.AddGameMode(hordeInfo);
            manager.AddGameMode(teamInfo);

            Debug.Log("Added game modes to GameModeManager");
        }
        else
        {
            Debug.LogError("Game mode prefabs not assigned");
        }
    }
}