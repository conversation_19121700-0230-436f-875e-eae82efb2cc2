using System;
using System.Collections.Generic;
using UnityEngine;
using Fusion;
using Projectiles;
/// <summary>
/// Manages game mode selection, configuration, and initialization.
/// </summary>
public class GameModeManager : ContextBehaviour
{
    [Serializable]
    public class GameModeInfo
    {
        public string modeName;
        public GameModeBase modePrefab;
        public WaveConfig defaultWaveConfig;
    }

    [Header("Game Modes")]
    [SerializeField] private List<GameModeInfo> availableGameModes = new();

    [Header("Default Settings")]
    [SerializeField] private int defaultGameModeIndex = 0;

    [Networked]
    private int SelectedGameModeIndex { get; set; }

    // Instead of using NetworkString which can cause JSON parsing issues,
    // we'll use a direct reference to the WaveConfig asset
    [Networked]
    private int SelectedWaveConfigIndex { get; set; }

    [SerializeField] private List<WaveConfig> availableWaveConfigs = new();

    private GameModeBase _activeGameMode;

    // Events
    public event Action<GameModeBase> OnGameModeSpawned;
    public event Action<int> OnGameModeIndexChanged;

    public override void Spawned()
    {
        if (Runner.IsServer)
        {
            // Set default game mode
            SelectedGameModeIndex = defaultGameModeIndex;

            // Set default wave config
            if (defaultGameModeIndex < availableGameModes.Count)
            {
                var defaultConfig = availableGameModes[defaultGameModeIndex].defaultWaveConfig;
                if (defaultConfig != null)
                {
                    // Find the index of the default config in our available configs
                    int configIndex = availableWaveConfigs.FindIndex(c => c == defaultConfig);
                    if (configIndex >= 0)
                    {
                        SelectedWaveConfigIndex = configIndex;
                        Debug.Log($"Set initial wave config index to {configIndex}");
                    }
                    else if (availableWaveConfigs.Count > 0)
                    {
                        // Fallback to first available config
                        SelectedWaveConfigIndex = 0;
                        Debug.Log("Default config not found in available configs, using first available");
                    }
                    else
                    {
                        Debug.LogError("No wave configs available!");
                    }
                }
            }
        }
    }

    // Called when the host changes the selected game mode
    public void SetGameMode(int gameModeIndex)
    {
        if (!Runner.IsServer) return;

        if (gameModeIndex >= 0 && gameModeIndex < availableGameModes.Count)
        {
            SelectedGameModeIndex = gameModeIndex;

            // Update config to match the new game mode
            var defaultConfig = availableGameModes[gameModeIndex].defaultWaveConfig;
            if (defaultConfig != null)
            {
                // Find the index of the config in our available configs
                int configIndex = availableWaveConfigs.FindIndex(c => c == defaultConfig);
                if (configIndex >= 0)
                {
                    SelectedWaveConfigIndex = configIndex;
                    Debug.Log($"Set wave config index to {configIndex} for game mode {availableGameModes[gameModeIndex].modeName}");
                }
                else if (availableWaveConfigs.Count > 0)
                {
                    // Fallback to first available config
                    SelectedWaveConfigIndex = 0;
                    Debug.LogWarning($"Config for {availableGameModes[gameModeIndex].modeName} not found in available configs, using first available");
                }
            }

            // Notify listeners about the change
            NotifyGameModeIndexChanged();

            Debug.Log($"Selected game mode: {availableGameModes[gameModeIndex].modeName}");
        }
    }

    // Called when the host updates the game configuration
    public void UpdateGameConfig(WaveConfig config)
    {
        if (!Runner.IsServer) return;

        if (config != null)
        {
            // Find or add the config to our available configs
            int configIndex = availableWaveConfigs.FindIndex(c => c == config);
            if (configIndex < 0)
            {
                // Add the config to our available configs
                availableWaveConfigs.Add(config);
                configIndex = availableWaveConfigs.Count - 1;
                Debug.Log($"Added new wave config at index {configIndex}");
            }

            // Set the selected config index
            SelectedWaveConfigIndex = configIndex;
            Debug.Log($"Updated selected wave config to index {configIndex}");
        }
    }

    // Called when the game starts
    public void StartGame()
    {
        if (!Runner.IsServer) return;

        // Spawn the selected game mode
        SpawnGameMode();
    }

    private void SpawnGameMode()
    {
        if (!Runner.IsServer) return;

        // Check if we have a valid game mode index
        if (SelectedGameModeIndex < 0 || SelectedGameModeIndex >= availableGameModes.Count)
        {
            Debug.LogError("Invalid game mode index!");
            return;
        }

        // Get the game mode prefab
        var gameModeInfo = availableGameModes[SelectedGameModeIndex];
        if (gameModeInfo.modePrefab == null)
        {
            Debug.LogError("Game mode prefab is null!");
            return;
        }

        // Spawn the game mode
        var gameModeObj = Runner.Spawn(gameModeInfo.modePrefab);
        _activeGameMode = gameModeObj.GetComponent<GameModeBase>();

        if (_activeGameMode == null)
        {
            Debug.LogError("Failed to get GameModeBase component from spawned object!");
            return;
        }

        // Configure the game mode
        ConfigureGameMode(_activeGameMode);

        // Start the game
        _activeGameMode.StartGame();

        // Notify listeners
        OnGameModeSpawned?.Invoke(_activeGameMode);

        Debug.Log($"Spawned and started game mode: {gameModeInfo.modeName}");
    }

    private void ConfigureGameMode(GameModeBase gameMode)
    {
        if (gameMode == null) return;

        // Get the selected wave config
        WaveConfig config = null;

        // Check if we have a valid wave config index
        if (SelectedWaveConfigIndex >= 0 && SelectedWaveConfigIndex < availableWaveConfigs.Count)
        {
            config = availableWaveConfigs[SelectedWaveConfigIndex];
            Debug.Log($"Using wave config at index {SelectedWaveConfigIndex}");
        }
        else
        {
            Debug.LogWarning($"Invalid wave config index: {SelectedWaveConfigIndex}, falling back to default");

            // Fallback to the default config for the selected game mode
            if (SelectedGameModeIndex >= 0 && SelectedGameModeIndex < availableGameModes.Count)
            {
                config = availableGameModes[SelectedGameModeIndex].defaultWaveConfig;
                Debug.Log("Using default wave config for selected game mode");
            }
        }

        // Apply the config to the game mode
        if (config != null)
        {
            if (gameMode is HordeGameMode hordeMode)
            {
                hordeMode.ApplyWaveConfig(config);
                Debug.Log("Applied wave config to HordeGameMode");
            }
            else if (gameMode is TeamBasedGameMode teamMode)
            {
                teamMode.ApplyWaveConfig(config);
                Debug.Log("Applied wave config to TeamBasedGameMode");
            }
        }
        else
        {
            Debug.LogError("No valid wave config found!");
        }
    }

    // Called when the selected game mode index changes
    public void NotifyGameModeIndexChanged()
    {
        OnGameModeIndexChanged?.Invoke(SelectedGameModeIndex);
    }

    // Get the current active game mode
    public GameModeBase GetActiveGameMode()
    {
        return _activeGameMode;
    }

    // Get the name of the currently selected game mode
    public string GetSelectedGameModeName()
    {
        if (SelectedGameModeIndex >= 0 && SelectedGameModeIndex < availableGameModes.Count)
        {
            return availableGameModes[SelectedGameModeIndex].modeName;
        }
        return "Unknown";
    }

    // Get the list of available game modes
    public List<string> GetAvailableGameModeNames()
    {
        List<string> names = new();
        foreach (var mode in availableGameModes)
        {
            names.Add(mode.modeName);
        }
        return names;
    }

    // Add a game mode to the available game modes list
    public void AddGameMode(GameModeInfo gameModeInfo)
    {
        if (!Runner.IsServer) return;

        // Check if the game mode already exists
        bool exists = false;
        foreach (var mode in availableGameModes)
        {
            if (mode.modeName == gameModeInfo.modeName)
            {
                exists = true;
                break;
            }
        }

        // Add the game mode if it doesn't exist
        if (!exists)
        {
            availableGameModes.Add(gameModeInfo);
            Debug.Log($"Added game mode: {gameModeInfo.modeName}");
        }
        else
        {
            Debug.Log($"Game mode {gameModeInfo.modeName} already exists");
        }
    }
}
